using System;
using System.Collections.Generic;
using System.Linq;
using KobiPanel.Models;

namespace KobiPanel.Helpers
{
    public static class BildirimHelper
    {
        public static void BildirimOlustur(KobiPanelDbContext db, string baslik, string mesaj, 
            string tip = "Bilgi", int? kullaniciId = null, string url = null, string ikon = null, int oncelik = 2)
        {
            try
            {
                var bildirim = new Bildirim
                {
                    Baslik = baslik,
                    Mesaj = mesaj,
                    Tip = tip,
                    KullaniciId = kullaniciId,
                    Url = url,
                    Ikon = ikon ?? GetDefaultIcon(tip),
                    Oncelik = oncelik,
                    Okundu = false,
                    Aktif = true,
                    OlusturmaTarihi = DateTime.Now
                };

                db.Bildirimler.Add(bildirim);
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                // Log the error but don't throw to prevent breaking the main operation
                System.Diagnostics.Debug.WriteLine("Bildirim oluşturma hatası: " + ex.Message);
            }
        }

        public static void SistemBildirimi(KobiPanelDbContext db, string baslik, string mesaj, string url = null)
        {
            BildirimOlustur(db, baslik, mesaj, "Sistem", null, url, "fas fa-cog", 2);
        }

        public static void UyariBildirimi(KobiPanelDbContext db, string baslik, string mesaj, int? kullaniciId = null, string url = null)
        {
            BildirimOlustur(db, baslik, mesaj, "Uyari", kullaniciId, url, "fas fa-exclamation-triangle", 3);
        }

        public static void BasariBildirimi(KobiPanelDbContext db, string baslik, string mesaj, int? kullaniciId = null, string url = null)
        {
            BildirimOlustur(db, baslik, mesaj, "Basari", kullaniciId, url, "fas fa-check-circle", 2);
        }

        public static void HataBildirimi(KobiPanelDbContext db, string baslik, string mesaj, int? kullaniciId = null, string url = null)
        {
            BildirimOlustur(db, baslik, mesaj, "Hata", kullaniciId, url, "fas fa-times-circle", 4);
        }

        private static string GetDefaultIcon(string tip)
        {
            switch (tip.ToLower())
            {
                case "bilgi":
                    return "fas fa-info-circle";
                case "uyari":
                    return "fas fa-exclamation-triangle";
                case "hata":
                    return "fas fa-times-circle";
                case "basari":
                    return "fas fa-check-circle";
                case "sistem":
                    return "fas fa-cog";
                default:
                    return "fas fa-bell";
            }
        }

        public static string GetBildirimClass(string tip)
        {
            switch (tip.ToLower())
            {
                case "bilgi":
                    return "alert-info";
                case "uyari":
                    return "alert-warning";
                case "hata":
                    return "alert-danger";
                case "basari":
                    return "alert-success";
                case "sistem":
                    return "alert-secondary";
                default:
                    return "alert-info";
            }
        }

        public static string GetBildirimColor(string tip)
        {
            switch (tip.ToLower())
            {
                case "bilgi":
                    return "text-info";
                case "uyari":
                    return "text-warning";
                case "hata":
                    return "text-danger";
                case "basari":
                    return "text-success";
                case "sistem":
                    return "text-secondary";
                default:
                    return "text-info";
            }
        }

        public static int OkunmamisBildirimSayisi(KobiPanelDbContext db, int? kullaniciId = null)
        {
            try
            {
                var query = db.Bildirimler.Where(b => b.Aktif && !b.Okundu);
                
                if (kullaniciId.HasValue)
                {
                    query = query.Where(b => b.KullaniciId == kullaniciId.Value || b.KullaniciId == null);
                }
                else
                {
                    query = query.Where(b => b.KullaniciId == null);
                }

                return query.Count();
            }
            catch
            {
                return 0;
            }
        }

        public static List<Bildirim> SonBildirimler(KobiPanelDbContext db, int? kullaniciId = null, int adet = 5)
        {
            try
            {
                var query = db.Bildirimler.Where(b => b.Aktif);
                
                if (kullaniciId.HasValue)
                {
                    query = query.Where(b => b.KullaniciId == kullaniciId.Value || b.KullaniciId == null);
                }
                else
                {
                    query = query.Where(b => b.KullaniciId == null);
                }

                return query.OrderByDescending(b => b.OlusturmaTarihi)
                           .Take(adet)
                           .ToList();
            }
            catch
            {
                return new List<Bildirim>();
            }
        }

        public static void BildirimiOkunduOlarakIsaretle(KobiPanelDbContext db, int bildirimId)
        {
            try
            {
                var bildirim = db.Bildirimler.Find(bildirimId);
                if (bildirim != null && !bildirim.Okundu)
                {
                    bildirim.Okundu = true;
                    bildirim.OkunmaTarihi = DateTime.Now;
                    db.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Bildirim okundu işaretleme hatası: " + ex.Message);
            }
        }

        public static void TumBildirimleriOkunduOlarakIsaretle(KobiPanelDbContext db, int? kullaniciId = null)
        {
            try
            {
                var query = db.Bildirimler.Where(b => b.Aktif && !b.Okundu);
                
                if (kullaniciId.HasValue)
                {
                    query = query.Where(b => b.KullaniciId == kullaniciId.Value || b.KullaniciId == null);
                }
                else
                {
                    query = query.Where(b => b.KullaniciId == null);
                }

                var bildirimler = query.ToList();
                foreach (var bildirim in bildirimler)
                {
                    bildirim.Okundu = true;
                    bildirim.OkunmaTarihi = DateTime.Now;
                }

                db.SaveChanges();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Tüm bildirimler okundu işaretleme hatası: " + ex.Message);
            }
        }
    }
}
