﻿
@{
    ViewBag.Title = "Genel Durum";
    Layout = "~/Views/Shared/_LayoutBos.cshtml";
}
<link href="~/Content/Mdb/css/addons-pro/cards-extended.min.css" rel="stylesheet" />
<section class="mt-md-4 pt-md-2 mb-5 pb-4">

    <!-- Grid row -->
    <div class="row">

        <!-- Grid column -->
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">

            <!-- Card -->
            <div class="card gradient-card">

                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">

                    <!-- Content -->

                    <div class="text-white d-flex h-100 mask blue-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Toplam Satış Sayısı</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.SatisSayisi ?? 0)</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="far fa-money-bill-alt fa-3x"></i>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Card -->

        </div>
        <!-- Grid column -->
        <!-- Grid column -->
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">

            <!-- Card -->
            <div class="card gradient-card">

                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">

                    <!-- Content -->

                    <div class="text-white d-flex h-100 mask purple-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Toplam Müşteri Sayısı</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.MusteriSayisi ?? 0)</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="far fa-money-bill-alt fa-3x"></i>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Card -->

        </div>

        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">

            <!-- Card -->
            <div class="card gradient-card">

                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">

                    <!-- Content -->

                    <div class="text-white d-flex h-100 mask peach-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Toplam Ürün Çeşidi</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.UrunSayisi ?? 0)</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="far fa-money-bill-alt fa-3x"></i>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Card -->

        </div>

        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">

            <!-- Card -->
            <div class="card gradient-card">

                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">

                    <!-- Content -->

                    <div class="text-white d-flex h-100 mask aqua-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Toplam Gider Sayısı</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.GiderSayisi ?? 0)</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="far fa-money-bill-alt fa-3x"></i>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Card -->

        </div>

    </div>
    <!-- Grid row -->
    <!-- Grid row -->
    <div class="row" style="margin-top:2em;">

        <!-- Grid column TOPLAM TUTAR -->
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">

            <!-- Card -->
            <div class="card gradient-card">

                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">

                    <!-- Content -->

                    <div class="text-white d-flex h-100 mask blue-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Toplam Satış Tutarı</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.ToplamTutar ?? "0") TL</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="far fa-money-bill-alt fa-3x"></i>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Card -->

        </div>
        <!-- Grid column -->
        <!-- Grid column -->
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">
            <!-- Card SatilanBuyukAdet -->
            <div class="card gradient-card">

                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">

                    <!-- Content -->

                    <div class="text-white d-flex h-100 mask purple-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Toplam Satilan Büyük Paket</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.SatilanBuyukAdet ?? 0)</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="far fa-money-bill-alt fa-3x"></i>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Card -->
        </div>
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">
            <!-- Card SatilanKucukAdet-->
            <div class="card gradient-card">

                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">

                    <!-- Content -->

                    <div class="text-white d-flex h-100 mask peach-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Toplam Satılan Küçük Paket Sayısı</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.SatilanKucukAdet ?? 0)</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="far fa-money-bill-alt fa-3x"></i>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Card -->
        </div>
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">

            <!-- Card SatilanSamanAdet -->
            <div class="card gradient-card">

                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">

                    <!-- Content -->

                    <div class="text-white d-flex h-100 mask aqua-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Toplam Satilan Saman Adeti</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.SatilanSamanAdet ?? 0)</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="far fa-money-bill-alt fa-3x"></i>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Card -->

        </div>

    </div>


    <!-- Grid row -->

    <div class="row" style="margin-top:2em;">

        <!-- Grid column TOPLAM TUTAR -->
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">

            <!-- Card -->
            <div class="card gradient-card">

                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">

                    <!-- Content -->

                    <div class="text-white d-flex h-100 mask blue-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Ortalama Satış Tutarı</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.OrtSatisTutari ?? "0") TL</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="far fa-money-bill-alt fa-3x"></i>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Card -->

        </div>
        <!-- Grid column -->
        <!-- Grid column -->
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">
            <!-- Card SatilanBuyukAdet -->
            <div class="card gradient-card">

                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">

                    <!-- Content -->

                    <div class="text-white d-flex h-100 mask purple-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Toplam Satilan Büyük Paket Tutarı</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.SatilanBuyukTutar ?? "0")</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="far fa-money-bill-alt fa-3x"></i>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Card -->
        </div>
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">
            <!-- Card SatilanKucukAdet-->
            <div class="card gradient-card">

                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">

                    <!-- Content -->

                    <div class="text-white d-flex h-100 mask peach-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Toplam Satılan Küçük Paket Tutarı</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.SatilanKucukTutar ?? "0") TL</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="far fa-money-bill-alt fa-3x"></i>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Card -->
        </div>
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">

            <!-- Card SatilanSamanAdet -->
            <div class="card gradient-card">

                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">

                    <!-- Content -->

                    <div class="text-white d-flex h-100 mask aqua-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Toplam Satilan Saman Tutarı</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.SatilanSamanTutar ?? "0") TL</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="far fa-money-bill-alt fa-3x"></i>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Card -->

        </div>

    </div>

    <!-- Grid row -->

    <!-- Yeni Özellikler Satırı -->
    <div class="row" style="margin-top:2em;">

        <!-- Araç Sayısı -->
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">
            <div class="card gradient-card">
                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">
                    <div class="text-white d-flex h-100 mask orange-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Toplam Araç Sayısı</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.AracSayisi ?? 0)</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="fas fa-truck fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tarla Sayısı -->
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">
            <div class="card gradient-card">
                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">
                    <div class="text-white d-flex h-100 mask green-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Biçilen Tarla Sayısı</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.TarlaSayisi ?? 0)</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="fas fa-seedling fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bu Ay Satış -->
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">
            <div class="card gradient-card">
                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">
                    <div class="text-white d-flex h-100 mask red-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Bu Ay Satış</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.BuAySatisSayisi ?? 0)</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="fas fa-chart-line fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bu Ay Gider -->
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">
            <div class="card gradient-card">
                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">
                    <div class="text-white d-flex h-100 mask pink-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Bu Ay Gider</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.BuAyGiderSayisi ?? 0)</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="fas fa-chart-pie fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Kar/Zarar Satırı -->
    <div class="row" style="margin-top:2em;">

        <!-- Toplam Gider -->
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">
            <div class="card gradient-card">
                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">
                    <div class="text-white d-flex h-100 mask red-gradient-rgba">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Toplam Gider</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.GiderToplami ?? "0") TL</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="fas fa-minus-circle fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Net Kar/Zarar -->
        <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">
            <div class="card gradient-card">
                <div class="card-image" style="background-image: url(https://mdbootstrap.com/img/Photos/Horizontal/Work/4-col/img%20%2814%29.jpg)">
                    @{
                        var karZararClass = ViewBag.KarZararDurum == "Kar" ? "green-gradient-rgba" : "red-gradient-rgba";
                        var karZararIcon = ViewBag.KarZararDurum == "Kar" ? "fas fa-arrow-up" : "fas fa-arrow-down";
                    }
                    <div class="text-white d-flex h-100 mask @karZararClass">
                        <div class="first-content align-self-center p-3">
                            <h3 class="card-title">Net @(ViewBag.KarZararDurum ?? "Durum")</h3>
                            <h3 class="font-weight-bold float-left">@(ViewBag.NetKar ?? "0") TL</h3>
                        </div>
                        <div class="second-content align-self-center mx-auto text-center">
                            <i class="@karZararIcon fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hızlı Erişim Butonları -->
        <div class="col-xl-6 col-md-12 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-rocket"></i> Hızlı Erişim</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <a href="@Url.Action("SatisEkle", "Satis")" class="btn btn-primary btn-block">
                                <i class="fas fa-plus"></i> Yeni Satış
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="@Url.Action("MusteriEkle", "Musteri")" class="btn btn-success btn-block">
                                <i class="fas fa-user-plus"></i> Yeni Müşteri
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="@Url.Action("GiderEkle", "Gider")" class="btn btn-warning btn-block">
                                <i class="fas fa-receipt"></i> Yeni Gider
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="@Url.Action("Index", "Rapor")" class="btn btn-info btn-block">
                                <i class="fas fa-chart-bar"></i> Raporlar
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="@Url.Action("YakitEkle", "Arac")" class="btn btn-secondary btn-block">
                                <i class="fas fa-gas-pump"></i> Yakıt Ekle
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="@Url.Action("TarlaEkle", "Bicme")" class="btn btn-dark btn-block">
                                <i class="fas fa-seedling"></i> Tarla Ekle
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>






