/* ========================================
   DARK/LIGHT THEME SYSTEM
   ======================================== */

/* CSS Variables for Light Theme (Default) */
:root {
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --bg-card: #ffffff;
    --bg-sidebar: #ffffff;

    /* Text Colors */
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #868e96;
    --text-light: #ffffff;

    /* Border Colors */
    --border-color: #dee2e6;
    --border-light: #f1f3f4;

    /* Brand Colors */
    --brand-primary: #007bff;
    --brand-secondary: #6c757d;
    --brand-success: #28a745;
    --brand-danger: #dc3545;
    --brand-warning: #ffc107;
    --brand-info: #17a2b8;

    /* Navbar Colors */
    --navbar-bg: rgba(255, 255, 255, 0.95);
    --navbar-text: #212529;
    --navbar-border: #dee2e6;

    /* Sidebar Colors */
    --sidebar-bg: #2e3951;
    --sidebar-text: #ffffff;
    --sidebar-hover: rgba(255, 255, 255, 0.1);

    /* Card Colors */
    --card-shadow: rgba(0, 0, 0, 0.1);
    --card-border: #dee2e6;

    /* Form Colors */
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --input-focus: #80bdff;

    /* Table Colors */
    --table-bg: #ffffff;
    --table-stripe: #f8f9fa;
    --table-border: #dee2e6;
    --table-hover: #f5f5f5;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    /* Background Colors */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3a3a3a;
    --bg-card: #2d2d2d;
    --bg-sidebar: #1e1e1e;

    /* Text Colors */
    --text-primary: #e0e0e0;
    --text-secondary: #b0b0b0;
    --text-muted: #888888;
    --text-light: #ffffff;

    /* Border Colors */
    --border-color: #404040;
    --border-light: #333333;

    /* Brand Colors (Slightly adjusted for dark theme) */
    --brand-primary: #4dabf7;
    --brand-secondary: #868e96;
    --brand-success: #51cf66;
    --brand-danger: #ff6b6b;
    --brand-warning: #ffd43b;
    --brand-info: #22b8cf;

    /* Navbar Colors */
    --navbar-bg: rgba(26, 26, 26, 0.95);
    --navbar-text: #e0e0e0;
    --navbar-border: #404040;

    /* Sidebar Colors */
    --sidebar-bg: #1e1e1e;
    --sidebar-text: #e0e0e0;
    --sidebar-hover: rgba(255, 255, 255, 0.1);

    /* Card Colors */
    --card-shadow: rgba(0, 0, 0, 0.3);
    --card-border: #404040;

    /* Form Colors */
    --input-bg: #3a3a3a;
    --input-border: #555555;
    --input-focus: #4dabf7;

    /* Table Colors */
    --table-bg: #2d2d2d;
    --table-stripe: #3a3a3a;
    --table-border: #404040;
    --table-hover: #404040;
}

/* ========================================
   GLOBAL THEME APPLICATIONS
   ======================================== */

/* Body and Main Elements */
body {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Cards */
.card {
    background-color: var(--bg-card) !important;
    border-color: var(--card-border) !important;
    color: var(--text-primary) !important;
    box-shadow: 0 2px 5px var(--card-shadow) !important;
    transition: all 0.3s ease;
}

.card-body {
    background-color: var(--bg-card) !important;
    color: var(--text-primary) !important;
}

.card-header {
    background-color: var(--bg-secondary) !important;
    border-bottom-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

/* Navbar */
.navbar {
    background-color: var(--navbar-bg) !important;
    border-bottom: 1px solid var(--navbar-border) !important;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px var(--card-shadow) !important;
}

.navbar .nav-link {
    color: var(--navbar-text) !important;
    transition: all 0.3s ease;
    border-radius: 4px;
    margin: 0 2px;
    padding: 8px 12px !important;
}

.navbar .nav-link:hover {
    color: var(--brand-primary) !important;
    background-color: var(--bg-secondary) !important;
}

.navbar .nav-link:focus {
    color: var(--brand-primary) !important;
}

/* Navbar Brand */
.navbar-brand {
    color: var(--text-primary) !important;
    font-weight: 600;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    color: var(--brand-primary) !important;
}

.navbar-brand img {
    transition: all 0.3s ease;
}

.navbar-brand:hover img {
    transform: scale(1.05);
}

/* Navbar Toggler */
.navbar-toggler {
    border-color: var(--border-color) !important;
    transition: all 0.3s ease;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(77, 171, 247, 0.25) !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

[data-theme="dark"] .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28224, 224, 224, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

/* Active Navigation Item */
.navbar-nav .nav-item.active .nav-link {
    color: var(--brand-primary) !important;
    background-color: var(--bg-secondary) !important;
    font-weight: 600;
}

/* Dropdown Menu Styling */
.navbar .dropdown-menu {
    background-color: var(--bg-card) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 6px var(--card-shadow) !important;
    border-radius: 8px !important;
    margin-top: 8px !important;
    padding: 8px 0 !important;
}

.navbar .dropdown-item {
    color: var(--text-primary) !important;
    transition: all 0.3s ease;
    padding: 8px 20px !important;
    border-radius: 4px !important;
    margin: 2px 8px !important;
}

.navbar .dropdown-item:hover {
    background-color: var(--bg-secondary) !important;
    color: var(--brand-primary) !important;
}

.navbar .dropdown-item:focus {
    background-color: var(--bg-secondary) !important;
    color: var(--brand-primary) !important;
}

.navbar .dropdown-item i {
    width: 20px;
    margin-right: 8px;
    text-align: center;
}

.navbar .dropdown-divider {
    border-top-color: var(--border-color) !important;
    margin: 8px 0 !important;
}

/* Responsive Navbar */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background-color: var(--bg-card) !important;
        border: 1px solid var(--border-color) !important;
        border-radius: 8px !important;
        margin-top: 10px !important;
        padding: 15px !important;
        box-shadow: 0 4px 6px var(--card-shadow) !important;
    }

    .navbar-nav .nav-link {
        padding: 12px 16px !important;
        margin: 2px 0 !important;
        border-radius: 6px !important;
    }

    .navbar .dropdown-menu {
        background-color: var(--bg-secondary) !important;
        border: none !important;
        box-shadow: none !important;
        margin-top: 0 !important;
        margin-left: 20px !important;
    }
}

/* Breadcrumb (if still used) */
.breadcrumb-dn p {
    color: var(--text-secondary) !important;
}

/* Sidebar styles removed - using top navbar only */

/* Forms */
.form-control {
    background-color: var(--input-bg) !important;
    border-color: var(--input-border) !important;
    color: var(--text-primary) !important;
    transition: all 0.3s ease;
}

.form-control:focus {
    background-color: var(--input-bg) !important;
    border-color: var(--input-focus) !important;
    color: var(--text-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(77, 171, 247, 0.25) !important;
}

.form-control::placeholder {
    color: var(--text-muted) !important;
}

/* Labels */
label {
    color: var(--text-primary) !important;
}

/* Tables */
.table {
    background-color: var(--table-bg) !important;
    color: var(--text-primary) !important;
}

.table th {
    background-color: var(--bg-secondary) !important;
    border-color: var(--table-border) !important;
    color: var(--text-primary) !important;
}

.table td {
    border-color: var(--table-border) !important;
    color: var(--text-primary) !important;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--table-stripe) !important;
}

.table-hover tbody tr:hover {
    background-color: var(--table-hover) !important;
}

/* Buttons */
.btn-primary {
    background-color: var(--brand-primary) !important;
    border-color: var(--brand-primary) !important;
}

.btn-secondary {
    background-color: var(--brand-secondary) !important;
    border-color: var(--brand-secondary) !important;
}

.btn-success {
    background-color: var(--brand-success) !important;
    border-color: var(--brand-success) !important;
}

.btn-danger {
    background-color: var(--brand-danger) !important;
    border-color: var(--brand-danger) !important;
}

.btn-warning {
    background-color: var(--brand-warning) !important;
    border-color: var(--brand-warning) !important;
}

.btn-info {
    background-color: var(--brand-info) !important;
    border-color: var(--brand-info) !important;
}

/* Alerts */
.alert {
    border-color: var(--border-color) !important;
}

.alert-success {
    background-color: rgba(81, 207, 102, 0.1) !important;
    border-color: var(--brand-success) !important;
    color: var(--brand-success) !important;
}

.alert-danger {
    background-color: rgba(255, 107, 107, 0.1) !important;
    border-color: var(--brand-danger) !important;
    color: var(--brand-danger) !important;
}

.alert-warning {
    background-color: rgba(255, 212, 59, 0.1) !important;
    border-color: var(--brand-warning) !important;
    color: var(--brand-warning) !important;
}

.alert-info {
    background-color: rgba(34, 184, 207, 0.1) !important;
    border-color: var(--brand-info) !important;
    color: var(--brand-info) !important;
}

/* Theme Toggle Button */
#theme-toggle {
    background: transparent !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    margin-left: 10px !important;
}

#theme-toggle:hover {
    background-color: var(--bg-secondary) !important;
    border-color: var(--brand-primary) !important;
    color: var(--brand-primary) !important;
    transform: scale(1.05) !important;
}

#theme-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(77, 171, 247, 0.25) !important;
    outline: none !important;
}

#theme-icon {
    font-size: 16px !important;
    transition: transform 0.3s ease !important;
}

/* Icon Animation */
.theme-transition {
    transform: rotate(180deg) !important;
}

/* Dropdown Menus */
.dropdown-menu {
    background-color: var(--bg-card) !important;
    border-color: var(--border-color) !important;
    box-shadow: 0 4px 6px var(--card-shadow) !important;
}

.dropdown-item {
    color: var(--text-primary) !important;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--bg-secondary) !important;
    color: var(--brand-primary) !important;
}

/* Badges */
.badge {
    background-color: var(--brand-danger) !important;
    color: var(--text-light) !important;
}

/* Text Colors */
.text-muted {
    color: var(--text-muted) !important;
}

.text-secondary {
    color: var(--text-secondary) !important;
}

/* Smooth Transitions for All Elements */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* ========================================
   SPECIFIC COMPONENT STYLES
   ======================================== */

/* Material Design Bootstrap Specific */
.gradient-card-header {
    background: var(--brand-primary) !important;
    color: var(--text-light) !important;
}

[data-theme="dark"] .gradient-card-header {
    background: linear-gradient(45deg, #1e3a5f, #4dabf7) !important;
}

/* Fixed Action Button */
.fixed-action-btn .btn-floating {
    background-color: var(--brand-primary) !important;
    transition: all 0.3s ease;
}

[data-theme="dark"] .fixed-action-btn .btn-floating {
    background-color: var(--brand-primary) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4) !important;
}

/* Sidebar Background Mask */
.sidenav-bg {
    background-color: var(--sidebar-bg) !important;
}

/* White Skin Override for Dark Theme */
[data-theme="dark"] .white-skin {
    background-color: var(--bg-primary) !important;
}

/* Card Cascade */
.card-cascade {
    background-color: var(--bg-card) !important;
    box-shadow: 0 4px 6px var(--card-shadow) !important;
}

/* Pagination */
.page-link {
    background-color: var(--bg-card) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

.page-link:hover {
    background-color: var(--bg-secondary) !important;
    border-color: var(--brand-primary) !important;
    color: var(--brand-primary) !important;
}

.page-item.active .page-link {
    background-color: var(--brand-primary) !important;
    border-color: var(--brand-primary) !important;
    color: var(--text-light) !important;
}

/* Modal */
.modal-content {
    background-color: var(--bg-card) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

.modal-header {
    background-color: var(--bg-secondary) !important;
    border-bottom-color: var(--border-color) !important;
}

.modal-footer {
    background-color: var(--bg-secondary) !important;
    border-top-color: var(--border-color) !important;
}

/* Breadcrumb */
.breadcrumb {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
}

.breadcrumb-item a {
    color: var(--brand-primary) !important;
}

.breadcrumb-item.active {
    color: var(--text-secondary) !important;
}

/* Progress Bars */
.progress {
    background-color: var(--bg-secondary) !important;
}

.progress-bar {
    background-color: var(--brand-primary) !important;
}

/* List Groups */
.list-group-item {
    background-color: var(--bg-card) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

.list-group-item:hover {
    background-color: var(--bg-secondary) !important;
}

.list-group-item.active {
    background-color: var(--brand-primary) !important;
    border-color: var(--brand-primary) !important;
    color: var(--text-light) !important;
}

/* Tooltips */
.tooltip-inner {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

.tooltip.bs-tooltip-top .arrow::before {
    border-top-color: var(--bg-tertiary) !important;
}

.tooltip.bs-tooltip-bottom .arrow::before {
    border-bottom-color: var(--bg-tertiary) !important;
}

.tooltip.bs-tooltip-left .arrow::before {
    border-left-color: var(--bg-tertiary) !important;
}

.tooltip.bs-tooltip-right .arrow::before {
    border-right-color: var(--bg-tertiary) !important;
}

/* Popovers */
.popover {
    background-color: var(--bg-card) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

.popover-header {
    background-color: var(--bg-secondary) !important;
    border-bottom-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

/* Select Dropdown */
.mdb-select .dropdown-content {
    background-color: var(--bg-card) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 6px var(--card-shadow) !important;
}

.mdb-select .dropdown-content li {
    color: var(--text-primary) !important;
}

.mdb-select .dropdown-content li:hover {
    background-color: var(--bg-secondary) !important;
}

.mdb-select .dropdown-content li.selected {
    background-color: var(--brand-primary) !important;
    color: var(--text-light) !important;
}

/* Date Picker */
.picker {
    background-color: var(--bg-card) !important;
    border-color: var(--border-color) !important;
}

.picker__header {
    background-color: var(--brand-primary) !important;
    color: var(--text-light) !important;
}

.picker__day {
    color: var(--text-primary) !important;
}

.picker__day:hover {
    background-color: var(--bg-secondary) !important;
}

.picker__day--selected {
    background-color: var(--brand-primary) !important;
    color: var(--text-light) !important;
}

/* Scrollbar Styling for Dark Theme */
[data-theme="dark"] ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Text Selection */
[data-theme="dark"] ::selection {
    background-color: rgba(77, 171, 247, 0.3);
    color: var(--text-light);
}

[data-theme="dark"] ::-moz-selection {
    background-color: rgba(77, 171, 247, 0.3);
    color: var(--text-light);
}

/* Focus Outline */
[data-theme="dark"] *:focus {
    outline-color: var(--brand-primary) !important;
}

/* Gradient Cards Override */
[data-theme="dark"] .gradient-card {
    background: linear-gradient(45deg, var(--bg-card), var(--bg-secondary)) !important;
}

/* Gradient Mask Overrides for Dark Theme */
[data-theme="dark"] .blue-gradient-rgba {
    background: linear-gradient(45deg, rgba(30, 58, 95, 0.9), rgba(77, 171, 247, 0.9)) !important;
}

[data-theme="dark"] .purple-gradient-rgba {
    background: linear-gradient(45deg, rgba(106, 17, 203, 0.9), rgba(168, 85, 247, 0.9)) !important;
}

[data-theme="dark"] .peach-gradient-rgba {
    background: linear-gradient(45deg, rgba(255, 107, 107, 0.9), rgba(255, 154, 158, 0.9)) !important;
}

[data-theme="dark"] .aqua-gradient-rgba {
    background: linear-gradient(45deg, rgba(34, 184, 207, 0.9), rgba(77, 208, 225, 0.9)) !important;
}

/* Card Image Dark Theme Overlay */
[data-theme="dark"] .card-image {
    position: relative;
}

[data-theme="dark"] .card-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

[data-theme="dark"] .card-image .mask {
    position: relative;
    z-index: 2;
}

/* Statistics Card Enhancements */
[data-theme="dark"] .gradient-card .card-title {
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .gradient-card .font-weight-bold {
    color: rgba(255, 255, 255, 1) !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
}

[data-theme="dark"] .gradient-card i {
    color: rgba(255, 255, 255, 0.8) !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* Chart Container */
.chart-container {
    background-color: var(--bg-card) !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin: 10px 0 !important;
    box-shadow: 0 2px 4px var(--card-shadow) !important;
}

/* Statistics Cards */
.stats-card {
    background-color: var(--bg-card) !important;
    border: 1px solid var(--border-color) !important;
    transition: all 0.3s ease !important;
}

.stats-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px var(--card-shadow) !important;
}

/* Loading Spinner */
.spinner-border {
    color: var(--brand-primary) !important;
}

/* Custom Scrollbar for Sidebar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

[data-theme="dark"] .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
}

/* Responsive Theme Toggle */
@media (max-width: 768px) {
    #theme-toggle {
        width: 35px !important;
        height: 35px !important;
        margin-left: 5px !important;
    }

    #theme-icon {
        font-size: 14px !important;
    }
}

/* Print Styles */
@media print {
    [data-theme="dark"] {
        --bg-primary: #ffffff !important;
        --bg-secondary: #f8f9fa !important;
        --text-primary: #212529 !important;
        --text-secondary: #6c757d !important;
    }

    #theme-toggle {
        display: none !important;
    }
}
