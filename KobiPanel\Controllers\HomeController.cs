﻿using KobiPanel.Helpers;
using KobiPanel.Models;
using KobiPanel.Models.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Security;
using System.Data.Entity;

namespace KobiPanel.Controllers
{


    public class HomeController : Controller
    {
        KobiPanelDbContext db = new KobiPanelDbContext();
        // GET: Home


        public ActionResult Index()
        {
            try
            {
                ViewBag.PageHeader = "Aydın Silaj Yönetim Paneli";

                // Temel istatistikler
                ViewBag.MusteriSayisi = db.Musteriler.Count();
                ViewBag.SatisSayisi = db.Satislar.Count();
                ViewBag.UrunSayisi = db.Urun.Count();
                ViewBag.GiderSayisi = db.Giderler.Count();
                ViewBag.NotSayisi = db.Not.Count();
                ViewBag.AracSayisi = db.Araclar.Count();
                ViewBag.TarlaSayisi = db.BicilenTarlalar.Count();

                // Bu ay istatistikleri
                var buAyBaslangic = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var buAyBitis = buAyBaslangic.AddMonths(1).AddDays(-1);

                ViewBag.BuAySatisSayisi = db.Satislar.Count(s => s.SatisTarihi >= buAyBaslangic && s.SatisTarihi <= buAyBitis);
                ViewBag.BuAyGiderSayisi = db.Giderler.Count(g => g.Tarih >= buAyBaslangic && g.Tarih <= buAyBitis);

                // Son aktiviteler
                ViewBag.SonSatislar = db.Satislar.Include(s => s.Musteriler).Include(s => s.Urun)
                    .OrderByDescending(s => s.SatisTarihi).Take(5).ToList();
                ViewBag.SonGiderler = db.Giderler.OrderByDescending(g => g.Tarih).Take(5).ToList();
                ViewBag.SonMusteriler = db.Musteriler.OrderByDescending(m => m.kayitTarihi).Take(5).ToList();

            var satislar = db.Satislar.ToList();

            decimal toplamtutar = 0;
            decimal SatilanKucukTutar = 0;
            int SatilanKucukAdet = 0;
            decimal SatilanBuyukTutar = 0;
            int SatilanBuyukAdet = 0;

            int SatilanSamanAdet = 0;

            decimal SatilanSamanTutar = 0;

            foreach (var item in satislar)
            {
                toplamtutar = toplamtutar + item.Tutar;
                if (item.UrunID == 5)
                {
                    SatilanKucukAdet = SatilanKucukAdet + item.UrunAdeti;
                    SatilanKucukTutar = SatilanKucukTutar + item.Tutar;
                }
                if (item.UrunID == 6)
                {
                    SatilanBuyukAdet = SatilanBuyukAdet + item.UrunAdeti;
                    SatilanBuyukTutar = SatilanBuyukTutar + item.Tutar;

                }
                if (item.UrunID == 7)
                {
                    SatilanSamanTutar = SatilanSamanTutar + item.Tutar;
                    SatilanSamanAdet = SatilanSamanAdet + item.UrunAdeti;
                }

            }
            ViewBag.SatilanSamanAdet = SatilanSamanAdet;
            ViewBag.SatilanSamanTutar = SatilanSamanTutar.ToString("0.##");


            ViewBag.SatilanKucukAdet = SatilanKucukAdet;
            ViewBag.SatilanKucukTutar = SatilanKucukTutar.ToString("0.##");
            ViewBag.SatilanBuyukAdet = SatilanBuyukAdet;
            ViewBag.SatilanBuyukTutar = SatilanBuyukTutar.ToString("0.##");

                ViewBag.ToplamTutar = toplamtutar;

                decimal gidertoplami = 0;

                foreach (var item in db.Giderler.ToList())
                {
                    if (item.Tutar.HasValue)
                    {
                        gidertoplami = gidertoplami + item.Tutar.Value;
                    }
                }

                ViewBag.GiderToplami = gidertoplami.ToString("0.##");

                if (ViewBag.SatisSayisi > 0)
                {
                    ViewBag.OrtSatisTutari = (toplamtutar / ViewBag.SatisSayisi).ToString("0.##");
                }
                else
                {
                    ViewBag.OrtSatisTutari = "0";
                }

                // Kar/Zarar hesaplama
                ViewBag.NetKar = (toplamtutar - gidertoplami).ToString("0.##");
                ViewBag.KarZararDurum = (toplamtutar - gidertoplami) >= 0 ? "Kar" : "Zarar";

                // Grafik verileri için aylık satış ve gider verileri
                var aylikVeriler = new List<object>();
                for (int i = 11; i >= 0; i--)
                {
                    var tarih = DateTime.Now.AddMonths(-i);
                    var ayBaslangic = new DateTime(tarih.Year, tarih.Month, 1);
                    var ayBitis = ayBaslangic.AddMonths(1).AddDays(-1);

                    var aylıkSatis = db.Satislar.Where(s => s.SatisTarihi >= ayBaslangic && s.SatisTarihi <= ayBitis).Sum(s => (decimal?)s.Tutar) ?? 0;
                    var aylıkGider = db.Giderler.Where(g => g.Tarih >= ayBaslangic && g.Tarih <= ayBitis && g.Tutar.HasValue).Sum(g => (decimal?)g.Tutar) ?? 0;

                    aylikVeriler.Add(new
                    {
                        Ay = tarih.ToString("MMM yyyy", new System.Globalization.CultureInfo("tr-TR")),
                        Satis = aylıkSatis,
                        Gider = aylıkGider
                    });
                }
                ViewBag.AylikVeriler = aylikVeriler;

                return View();
            }
            catch (Exception ex)
            {
                TempData["DashboardHata"] = "Dashboard yüklenirken bir hata oluştu: " + ex.Message;

                // Exception durumunda ViewBag'leri güvenli değerlerle set et
                ViewBag.PageHeader = "Aydın Silaj Yönetim Paneli";
                ViewBag.MusteriSayisi = 0;
                ViewBag.SatisSayisi = 0;
                ViewBag.UrunSayisi = 0;
                ViewBag.GiderSayisi = 0;
                ViewBag.NotSayisi = 0;
                ViewBag.AracSayisi = 0;
                ViewBag.TarlaSayisi = 0;
                ViewBag.BuAySatisSayisi = 0;
                ViewBag.BuAyGiderSayisi = 0;
                ViewBag.SonSatislar = new List<object>();
                ViewBag.SonGiderler = new List<object>();
                ViewBag.SonMusteriler = new List<object>();
                ViewBag.ToplamTutar = 0;
                ViewBag.GiderToplami = "0";
                ViewBag.OrtSatisTutari = "0";
                ViewBag.NetKar = "0";
                ViewBag.KarZararDurum = "Kar";
                ViewBag.AylikVeriler = new List<object>();
                ViewBag.SatilanSamanAdet = 0;
                ViewBag.SatilanSamanTutar = "0";
                ViewBag.SatilanKucukAdet = 0;
                ViewBag.SatilanKucukTutar = "0";
                ViewBag.SatilanBuyukAdet = 0;
                ViewBag.SatilanBuyukTutar = "0";

                return View();
            }
        }


        [HttpGet]
        [AllowAnonymous]
        public ActionResult Login()
        {
            // Boş model gönder ki View'de null reference hatası olmasın
            return View(new LoginViewModel());
        }

        [HttpPost]
        [AllowAnonymous,ValidateAntiForgeryToken]
        public ActionResult Login(LoginViewModel model,string ReturnUrl)
        {
            if (model == null)
            {
                ModelState.AddModelError("", "Geçersiz giriş bilgileri");
                return View(new LoginViewModel());
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var u = db.Kullanicilar.FirstOrDefault(m => m.KullaniciAdi == model.KullaniciAdi && m.Sifre == model.Sifre);
                    if (u != null)
                    {
                        FormsAuthentication.SetAuthCookie(model.KullaniciAdi, true);
                        Session["User"] = u; // Session'a kullanıcı bilgisini ekle

                        return string.IsNullOrEmpty(ReturnUrl) ? RedirectToAction("Index", "Home") : (ActionResult)Redirect(ReturnUrl);
                    }
                    else
                    {
                        ModelState.AddModelError("", "Kullanıcı adı veya şifre yanlış");
                    }
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "Giriş işlemi sırasında bir hata oluştu: " + ex.Message);
                }
            }
            return View(model);
        }


        public ActionResult LogOff()
        {
            try
            {
                FormsAuthentication.SignOut();
                if (Session != null)
                {
                    Session.Clear();
                    Session.Abandon();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("LogOff hatası: " + ex.Message);
            }

            return RedirectToAction("Login");
        }

        public ActionResult GunOzeti()
        {
            var gunluksatislar = db.Satislar.Where(m => m.SatisTarihi.Day == DateTime.Now.Day).ToList();
            var gunlukgiderler = db.Giderler.Where(m => m.Tarih.Day == DateTime.Now.Day).ToList();

            decimal gunlukgelir =0;
            decimal gunlukgider =0;

            foreach (var item in gunluksatislar)
            {
                gunlukgelir = gunlukgelir + item.Tutar;
            }

            foreach (var item in gunlukgiderler)
            {
                if (item.Tutar.HasValue)
                {
                    gunlukgider = gunlukgider + item.Tutar.Value;
                }
            }



            ViewBag.GunlukSatis = gunluksatislar;
            ViewBag.GunlukGider = gunlukgiderler;

            return View();
        }


    }
}