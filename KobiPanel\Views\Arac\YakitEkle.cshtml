﻿@model KobiPanel.Models.Vehicles.AlinanYakitlar

@{
    ViewBag.Title = "Yakıt Kaydı Ekle";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- Başarı/<PERSON>ları -->
@if (TempData["YakitBasarili"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <strong>Başarılı!</strong> @TempData["YakitBasarili"]
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

@if (TempData["YakitHata"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <strong>Hata!</strong> @TempData["YakitHata"]
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

@using (Html.BeginForm("YakitEkle", "Arac", FormMethod.Post, new { @class = "needs-validation", novalidate = "novalidate" }))
{
    @Html.AntiForgeryToken()

    <div class="row">
        <div class="col-md-8">
            @Html.ValidationSummary(true, "", new { @class = "alert alert-danger" })

            <!-- Araç Seçimi -->
            <div class="md-form">
                @Html.DropDownList("AracID", ViewBag.AracID as SelectList, "-- Araç Seçiniz --", new { @class = "mdb-select md-form", @required = "required" })
                <label for="AracID" class="active">Araç Plakası</label>
                @Html.ValidationMessageFor(model => model.AracID, "", new { @class = "text-danger" })
            </div>

            <!-- Yakıt Tutarı -->
            <div class="md-form">
                @Html.EditorFor(model => model.YakitTutari, new { htmlAttributes = new { @class = "form-control", @step = "0.01", @min = "0.01", @required = "required" } })
                @Html.LabelFor(model => model.YakitTutari, htmlAttributes: new { @class = "active" })
                @Html.ValidationMessageFor(model => model.YakitTutari, "", new { @class = "text-danger" })
            </div>

            <!-- Litre -->
            <div class="md-form">
                @Html.EditorFor(model => model.Litre, new { htmlAttributes = new { @class = "form-control", @step = "0.1", @min = "0.1", @required = "required" } })
                @Html.LabelFor(model => model.Litre, htmlAttributes: new { @class = "active" })
                @Html.ValidationMessageFor(model => model.Litre, "", new { @class = "text-danger" })
            </div>

            <!-- Yakıtın Alındığı Yer -->
            <div class="md-form">
                @Html.EditorFor(model => model.YakitinAlindigiYer, new { htmlAttributes = new { @class = "form-control", @required = "required", @maxlength = "100" } })
                @Html.LabelFor(model => model.YakitinAlindigiYer, htmlAttributes: new { @class = "active" })
                @Html.ValidationMessageFor(model => model.YakitinAlindigiYer, "", new { @class = "text-danger" })
            </div>

            <!-- KM Bilgisi -->
            <div class="md-form">
                @Html.EditorFor(model => model.YakitinAlindigiKM, new { htmlAttributes = new { @class = "form-control", @min = "0" } })
                @Html.LabelFor(model => model.YakitinAlindigiKM, htmlAttributes: new { @class = "active" })
                @Html.ValidationMessageFor(model => model.YakitinAlindigiKM, "", new { @class = "text-danger" })
            </div>

            <!-- Alış Tarihi -->
            <div class="md-form">
                @Html.EditorFor(model => model.AlisTarihi, new { htmlAttributes = new { @class = "form-control", @type = "date", @required = "required", @value = DateTime.Now.ToString("yyyy-MM-dd") } })
                @Html.LabelFor(model => model.AlisTarihi, htmlAttributes: new { @class = "active" })
                @Html.ValidationMessageFor(model => model.AlisTarihi, "", new { @class = "text-danger" })
            </div>

            <!-- Açıklama -->
            <div class="md-form">
                @Html.TextAreaFor(model => model.Aciklama, new { @class = "md-textarea form-control", @rows = "3", @maxlength = "500" })
                @Html.LabelFor(model => model.Aciklama, htmlAttributes: new { @class = "active" })
                @Html.ValidationMessageFor(model => model.Aciklama, "", new { @class = "text-danger" })
            </div>

            <!-- Butonlar -->
            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary waves-effect waves-light">
                    <i class="fas fa-save"></i> Kaydet
                </button>
                <a href="@Url.Action("YakitAnasayfa", "Arac")" class="btn btn-secondary waves-effect">
                    <i class="fas fa-arrow-left"></i> Listeye Dön
                </a>
            </div>
        </div>

        <!-- Bilgi Paneli -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Bilgi</h5>
                </div>
                <div class="card-body">
                    <p><strong>Yakıt Kaydı Ekleme</strong></p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> Araç seçimi zorunludur</li>
                        <li><i class="fas fa-check text-success"></i> Tutar ve litre bilgisi gereklidir</li>
                        <li><i class="fas fa-check text-success"></i> Litre başına fiyat otomatik hesaplanır</li>
                        <li><i class="fas fa-check text-success"></i> KM bilgisi isteğe bağlıdır</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
}

<script>
$(document).ready(function() {
    // Material Select initialization
    $('.mdb-select').materialSelect();

    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;

        // Araç seçimi kontrolü
        if ($('#AracID').val() === '') {
            isValid = false;
            $('#AracID').addClass('is-invalid');
        } else {
            $('#AracID').removeClass('is-invalid');
        }

        if (!isValid) {
            e.preventDefault();
            toastr.error('Lütfen tüm zorunlu alanları doldurun.');
        }
    });

    // Litre başına fiyat hesaplama
    $('#YakitTutari, #Litre').on('input', function() {
        var tutar = parseFloat($('#YakitTutari').val()) || 0;
        var litre = parseFloat($('#Litre').val()) || 0;

        if (tutar > 0 && litre > 0) {
            var litreBasinaFiyat = (tutar / litre).toFixed(2);
            $('#litre-fiyat-info').text('Litre başına fiyat: ' + litreBasinaFiyat + ' TL');
        } else {
            $('#litre-fiyat-info').text('');
        }
    });
});
</script>

<div id="litre-fiyat-info" class="text-info mt-2"></div>
