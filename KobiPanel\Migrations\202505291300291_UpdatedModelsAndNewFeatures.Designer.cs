// <auto-generated />
namespace KobiPanel.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.2.0-61023")]
    public sealed partial class UpdatedModelsAndNewFeatures : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(UpdatedModelsAndNewFeatures));
        
        string IMigrationMetadata.Id
        {
            get { return "202505291300291_UpdatedModelsAndNewFeatures"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
