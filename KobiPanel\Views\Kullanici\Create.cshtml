@model KobiPanel.Models.Kullanicilar

@{
    ViewBag.Title = "<PERSON><PERSON>";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@using (Html.BeginForm("Create", "<PERSON>llanici", FormMethod.Post, new { @class = "needs-validation", novalidate = "novalidate" }))
{
    @Html.AntiForgeryToken()
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user-plus"></i> <PERSON><PERSON>ilgiler<PERSON></h5>
                </div>
                <div class="card-body">
                    @Html.ValidationSummary(true, "", new { @class = "alert alert-danger" })

                    <!-- Ad Soyad -->
                    <div class="md-form">
                        @Html.EditorFor(model => model.AdSoyad, new { htmlAttributes = new { @class = "form-control", @required = "required", @maxlength = "100" } })
                        @Html.LabelFor(model => model.AdSoyad, htmlAttributes: new { @class = "active" })
                        @Html.ValidationMessageFor(model => model.AdSoyad, "", new { @class = "text-danger" })
                    </div>

                    <!-- Kullanıcı Adı -->
                    <div class="md-form">
                        @Html.EditorFor(model => model.KullaniciAdi, new { htmlAttributes = new { @class = "form-control", @required = "required", @maxlength = "50" } })
                        @Html.LabelFor(model => model.KullaniciAdi, htmlAttributes: new { @class = "active" })
                        @Html.ValidationMessageFor(model => model.KullaniciAdi, "", new { @class = "text-danger" })
                        <small class="form-text text-muted">Kullanıcı adı benzersiz olmalıdır.</small>
                    </div>

                    <!-- E-posta -->
                    <div class="md-form">
                        @Html.EditorFor(model => model.Email, new { htmlAttributes = new { @class = "form-control", @type = "email", @required = "required", @maxlength = "100" } })
                        @Html.LabelFor(model => model.Email, htmlAttributes: new { @class = "active" })
                        @Html.ValidationMessageFor(model => model.Email, "", new { @class = "text-danger" })
                    </div>

                    <!-- Telefon -->
                    <div class="md-form">
                        @Html.EditorFor(model => model.Telefon, new { htmlAttributes = new { @class = "form-control", @type = "tel", @maxlength = "15" } })
                        @Html.LabelFor(model => model.Telefon, htmlAttributes: new { @class = "active" })
                        @Html.ValidationMessageFor(model => model.Telefon, "", new { @class = "text-danger" })
                    </div>

                    <!-- Rol -->
                    <div class="md-form">
                        @Html.DropDownListFor(model => model.Rol, ViewBag.Roller as SelectList, "-- Rol Seçiniz --", new { @class = "mdb-select md-form", @required = "required" })
                        <label for="Rol" class="active">Kullanıcı Rolü</label>
                        @Html.ValidationMessageFor(model => model.Rol, "", new { @class = "text-danger" })
                    </div>

                    <!-- Şifre -->
                    <div class="md-form">
                        @Html.PasswordFor(model => model.Sifre, new { @class = "form-control", @required = "required", @minlength = "6" })
                        @Html.LabelFor(model => model.Sifre, htmlAttributes: new { @class = "active" })
                        @Html.ValidationMessageFor(model => model.Sifre, "", new { @class = "text-danger" })
                        <small class="form-text text-muted">Şifre en az 6 karakter olmalıdır.</small>
                    </div>

                    <!-- Şifre Tekrarı -->
                    <div class="md-form">
                        @Html.PasswordFor(model => model.SifreTekrar, new { @class = "form-control", @required = "required", @minlength = "6" })
                        @Html.LabelFor(model => model.SifreTekrar, htmlAttributes: new { @class = "active" })
                        @Html.ValidationMessageFor(model => model.SifreTekrar, "", new { @class = "text-danger" })
                    </div>

                    <!-- Açıklama -->
                    <div class="md-form">
                        @Html.TextAreaFor(model => model.Aciklama, new { @class = "md-textarea form-control", @rows = "3", @maxlength = "500" })
                        @Html.LabelFor(model => model.Aciklama, htmlAttributes: new { @class = "active" })
                        @Html.ValidationMessageFor(model => model.Aciklama, "", new { @class = "text-danger" })
                    </div>

                    <!-- Aktif Durumu -->
                    <div class="form-check">
                        @Html.CheckBoxFor(model => model.Aktif, new { @class = "form-check-input", @checked = "checked" })
                        @Html.LabelFor(model => model.Aktif, "Kullanıcı aktif olsun", new { @class = "form-check-label" })
                    </div>
                </div>
                <div class="card-footer text-center">
                    <button type="submit" class="btn btn-primary waves-effect waves-light">
                        <i class="fas fa-save"></i> Kullanıcı Ekle
                    </button>
                    <a href="@Url.Action("Index", "Kullanici")" class="btn btn-secondary waves-effect">
                        <i class="fas fa-arrow-left"></i> Geri Dön
                    </a>
                </div>
            </div>
        </div>

        <!-- Bilgi Paneli -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Kullanıcı Ekleme Bilgileri</h5>
                </div>
                <div class="card-body">
                    <h6><i class="fas fa-user-tag"></i> Roller Hakkında</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-crown text-danger"></i> <strong>Yönetici:</strong> Tüm yetkilere sahip</li>
                        <li><i class="fas fa-user text-primary"></i> <strong>Kullanıcı:</strong> Standart yetkiler</li>
                        <li><i class="fas fa-eye text-secondary"></i> <strong>Misafir:</strong> Sadece görüntüleme</li>
                    </ul>

                    <hr>

                    <h6><i class="fas fa-shield-alt"></i> Güvenlik</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> Şifreler güvenli şekilde saklanır</li>
                        <li><i class="fas fa-check text-success"></i> E-posta adresleri benzersiz olmalıdır</li>
                        <li><i class="fas fa-check text-success"></i> Kullanıcı adları benzersiz olmalıdır</li>
                    </ul>

                    <hr>

                    <h6><i class="fas fa-lightbulb"></i> İpuçları</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-info text-info"></i> Güçlü şifre kullanın</li>
                        <li><i class="fas fa-info text-info"></i> Geçerli e-posta adresi girin</li>
                        <li><i class="fas fa-info text-info"></i> Açıklama alanı isteğe bağlıdır</li>
                    </ul>
                </div>
            </div>

            <!-- Şifre Gücü Göstergesi -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-key"></i> Şifre Gücü</h6>
                </div>
                <div class="card-body">
                    <div class="progress" style="height: 10px;">
                        <div id="password-strength" class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small id="password-strength-text" class="text-muted">Şifre girin</small>
                </div>
            </div>
        </div>
    </div>
}

<script>
$(document).ready(function() {
    // Material Select initialization
    $('.mdb-select').materialSelect();
    
    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;
        
        // Şifre eşleşme kontrolü
        var password = $('#Sifre').val();
        var confirmPassword = $('#SifreTekrar').val();
        
        if (password !== confirmPassword) {
            isValid = false;
            $('#SifreTekrar').addClass('is-invalid');
            toastr.error('Şifreler eşleşmiyor.');
        } else {
            $('#SifreTekrar').removeClass('is-invalid');
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });
    
    // Şifre gücü kontrolü
    $('#Sifre').on('input', function() {
        var password = $(this).val();
        var strength = calculatePasswordStrength(password);
        
        $('#password-strength').css('width', strength.percentage + '%');
        $('#password-strength').removeClass('bg-danger bg-warning bg-success');
        $('#password-strength').addClass(strength.class);
        $('#password-strength-text').text(strength.text);
    });
    
    function calculatePasswordStrength(password) {
        var score = 0;
        var feedback = [];
        
        if (password.length >= 6) score += 20;
        if (password.length >= 8) score += 20;
        if (/[a-z]/.test(password)) score += 20;
        if (/[A-Z]/.test(password)) score += 20;
        if (/[0-9]/.test(password)) score += 20;
        
        if (score < 40) {
            return { percentage: score, class: 'bg-danger', text: 'Zayıf şifre' };
        } else if (score < 80) {
            return { percentage: score, class: 'bg-warning', text: 'Orta güçlükte şifre' };
        } else {
            return { percentage: score, class: 'bg-success', text: 'Güçlü şifre' };
        }
    }
    
    // Real-time validation
    $('#KullaniciAdi').on('blur', function() {
        var username = $(this).val();
        if (username.length > 0) {
            // AJAX ile kullanıcı adı kontrolü yapılabilir
        }
    });
    
    $('#Email').on('blur', function() {
        var email = $(this).val();
        if (email.length > 0) {
            // AJAX ile e-posta kontrolü yapılabilir
        }
    });
});
</script>
