using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace KobiPanel.Models
{
    [Table("ErrorLogs")]
    public class ErrorLog
    {
        [Key]
        public int ErrorID { get; set; }

        [Required]
        [StringLength(500)]
        public string ErrorMessage { get; set; }

        [Column(TypeName = "text")]
        public string StackTrace { get; set; }

        [StringLength(200)]
        public string ControllerName { get; set; }

        [StringLength(200)]
        public string ActionName { get; set; }

        [StringLength(50)]
        public string HttpMethod { get; set; }

        [StringLength(500)]
        public string RequestUrl { get; set; }

        [StringLength(45)]
        public string UserIP { get; set; }

        [StringLength(100)]
        public string UserAgent { get; set; }

        [StringLength(100)]
        public string KullaniciAdi { get; set; }

        [Required]
        public DateTime ErrorDate { get; set; }

        [StringLength(100)]
        public string ExceptionType { get; set; }

        [Column(TypeName = "text")]
        public string InnerException { get; set; }

        [StringLength(50)]
        public string Severity { get; set; } // Critical, High, Medium, Low

        public bool IsResolved { get; set; } = false;

        [Column(TypeName = "text")]
        public string ResolutionNotes { get; set; }

        public DateTime? ResolvedDate { get; set; }

        [StringLength(100)]
        public string ResolvedBy { get; set; }
    }
}
