﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using System.Web.Script.Serialization;
using KobiPanel.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace KobiPanel.Controllers
{
    public class MusteriController : Controller
    {
        private KobiPanelDbContext db = new KobiPanelDbContext();

        public ActionResult MusteriAnasayfaGrid()
        {

            var model = db.Musteriler.ToList();

            return View(model);
        }

        public ActionResult MusteriAnasayfa()
        {
            ViewBag.PageHeader = "Müşteri Listesi";

            var musteriler = db.Musteriler.Include(m => m.City).Include(m => m.Neighborhood).Include(m => m.Town);
            return View(musteriler.ToList());
        }

        public ActionResult MusteriDetay(int? id)
        {
            ViewBag.PageHeader = "Müşteri Detayları";

            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Musteriler musteriler = db.Musteriler.Find(id);
            if (musteriler == null)
            {
                return HttpNotFound();
            }
            return View(musteriler);
        }

        // GET: Musteri/Create
        public ActionResult MusteriEkle()
        {
            try
            {
                ViewBag.PageHeader = "Yeni Müşteri Oluştur";

                ViewBag.CityID = new SelectList(db.City.ToList(), "CityID", "CityName");
                List<string> d = new List<string>();
                d.Add("Seçim Yapınız");
                ViewBag.NeighborhoodID = new SelectList(d);
                ViewBag.TownID = new SelectList(d);
                // Boş model gönder ki View'de null reference hatası olmasın
                return View(new Musteriler());
            }
            catch (Exception ex)
            {
                TempData["MusteriEkleHata"] = "Sayfa yüklenirken bir hata oluştu: " + ex.Message;
                return RedirectToAction("MusteriAnasayfa");
            }
        }


        public ActionResult MusteriDuzenle(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }

            var model = db.Musteriler.Find(id);
            if (model == null)
            {
                return HttpNotFound();
            }

            ViewBag.CityID = new SelectList(db.City.ToList(), "CityID", "CityName");
            List<string> d = new List<string>();
            d.Add("Seçim Yapınız");
            ViewBag.NeighborhoodID = new SelectList(d);
            ViewBag.TownID = new SelectList(d);

            return View(model);
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult MusteriDuzenle(Musteriler musteriler)
        {

            if (ModelState.IsValid)
            {
                //db.Entry(urun).State = EntityState.Modified;

                musteriler.DuzenlemeTarihi = DateTime.Now;
                db.Entry(musteriler).State = EntityState.Modified;
                int sonuc = db.SaveChanges();

                if (sonuc > 0)
                {
                    TempData["musteriduzenlebasarili"] = "Musteri Düzenleme İşlemi Başarılı.";
                }
                else
                {
                    TempData["musteriduzenlebasarisiz."] = "Musteri Düzenleme İşlemi Başarısız.";
                }
                return RedirectToAction("MusteriAnasayfa");
            }
            return View(musteriler);
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult MusteriEkle(Musteriler musteriler)
        {

            ViewBag.PageHeader = "Yeni Müşteri Oluştur";

            if (ModelState.IsValid)
            {
                musteriler.kayitTarihi = DateTime.Now;
                musteriler.DuzenlemeTarihi = DateTime.Now;
                db.Musteriler.Add(musteriler);
                int sonuc = db.SaveChanges();

                if (sonuc>0)
                {
                    TempData["musterieklebasarili"] = "Musteri Ekleme İşlemi Başarılı.";
                }
                else
                {
                    TempData["musterieklebasarisiz."] = "Musteri Ekleme İşlemi Başarısız.";
                }

                return RedirectToAction("MusteriAnasayfa");
            }
            List<string> d = new List<string>();
            d.Add("Seçim Yapınız");

            if (musteriler.CityID.HasValue && musteriler.CityID > 0)
            {
                ViewBag.CityID = new SelectList(db.City, "CityID", "CityName", musteriler.CityID);
            }
            else
            {
                ViewBag.CityID = new SelectList(db.City, "CityID", "CityName");
            }
            if (musteriler.NeighborhoodID.HasValue && musteriler.NeighborhoodID > 0)
            {
                ViewBag.NeighborhoodID = new SelectList(db.Neighborhood, "NeighborhoodID", "NeighborhoodName", musteriler.NeighborhoodID);

            }
            else
            {
                ViewBag.NeighborhoodID = new SelectList(d);
            }
            if (musteriler.TownID.HasValue && musteriler.TownID > 0)
            {

                ViewBag.TownID = new SelectList(db.Town, "TownID", "TownName", musteriler.TownID);
            }
            else
            {
                ViewBag.TownID = new SelectList(d);
            }



            return View(musteriler);
        }


        public ActionResult MusteriSil(int? id)
        {
            ViewBag.PageHeader = "Müşteri Silme İşlemi";

            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Musteriler musteriler = db.Musteriler.Find(id);
            if (musteriler == null)
            {
                return HttpNotFound();
            }
            return View(musteriler);
        }


        [HttpPost, ActionName("MusteriSil")]
        [ValidateAntiForgeryToken]
        public ActionResult MusteriSilOnayli(int id)
        {
            try
            {
                Musteriler musteriler = db.Musteriler.Find(id);
                if (musteriler == null)
                {
                    TempData["silmeislemi"] = "Silinmek istenen müşteri bulunamadı.";
                    return RedirectToAction("MusteriAnasayfa");
                }

                string musteriAdi = musteriler.adsoyad; // Silmeden önce adı sakla
                db.Musteriler.Remove(musteriler);
                int silmesonuc = db.SaveChanges();

                if (silmesonuc > 0)
                {
                    TempData["silmeislemi"] = musteriAdi + " isimli müşteri başarıyla silindi.";
                }
                else
                {
                    TempData["silmeislemi"] = "Müşteri silme işlemi başarısız oldu.";
                }
            }
            catch (Exception ex)
            {
                TempData["silmeislemi"] = "Müşteri silme işlemi sırasında bir hata oluştu: " + ex.Message;
            }

            return RedirectToAction("MusteriAnasayfa");
        }



        public JsonResult GetTowns(int Id)
        {
            var List = db.Town.Where(m => m.CityID == Id).Select(x => new { x.TownID, x.TownName }).ToList();

            return Json(List, JsonRequestBehavior.AllowGet);
        }


        public JsonResult GetNeighborhood(int Id)
        {
            List<Neighborhood> List = db.Neighborhood.SqlQuery("select n.DistrictID, n.NeighborhoodID,n.NeighborhoodName ,n.ZipCode from Town t left join District d on t.TownID=d.TownID inner join Neighborhood n on n.DistrictID=d.DistrictID where t.TownID=" + Id).Select(m => new Neighborhood {NeighborhoodID= m.NeighborhoodID,NeighborhoodName=m.NeighborhoodName }).ToList();

            return Json(List, JsonRequestBehavior.AllowGet);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }


        #region SemtListGetir Tam çalışmıyor.

        //[HttpPost]
        //[AllowAnonymous]
        //public JsonResult semtList(int id)
        //{
        //    List<District> Semtler = db.District.Where(m => m.TownID == id).ToList();

        //    foreach (var item in Semtler)
        //    {
        //        List<SelectListItem> listItems = (from semt in db.Neighborhood.Where(m => m.DistrictID == m.DistrictID).ToList()
        //                                          select new SelectListItem()
        //                                          {
        //                                              Text = semt.NeighborhoodName,
        //                                              Value = semt.NeighborhoodID.ToString()
        //                                          }
        //                                          ).ToList();
        //        Mahalleler.AddRange(listItems);
        //    }

        //    ViewBag.MahalleListesi = Mahalleler;
        //    return Json(ViewBag.MahalleListesi, JsonRequestBehavior.AllowGet);

        //}

        #endregion
    }
}
