using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web.Mvc;
using KobiPanel.Helpers;
using KobiPanel.Models;
using System.Globalization;
using KobiPanel.Models.Vehicles;
using KobiPanel.Models.Bicme;

namespace KobiPanel.Controllers
{
    public class RaporController : Controller
    {
        private KobiPanelDbContext db = new KobiPanelDbContext();

        // GET: Rapor
        public ActionResult Index()
        {
            ViewBag.PageHeader = "Raporlar";
            return View();
        }

        // Satış Raporları
        public ActionResult SatisRaporlari()
        {
            ViewBag.PageHeader = "Satış Raporları";
            return View();
        }

        [HttpPost]
        public ActionResult SatisRaporlari(DateTime? baslangicTarihi, DateTime? bitisTarihi, int? musteriId)
        {
            try
            {
                ViewBag.PageHeader = "Satış Raporları";

                var query = db.Satislar.Include(s => s.MusteriID).Include(s => s.Urun).AsQueryable();

                if (baslangicTarihi.HasValue)
                {
                    query = query.Where(s => s.SatisTarihi >= baslangicTarihi.Value);
                    ViewBag.BaslangicTarihi = baslangicTarihi.Value.ToString("yyyy-MM-dd");
                }

                if (bitisTarihi.HasValue)
                {
                    query = query.Where(s => s.SatisTarihi <= bitisTarihi.Value);
                    ViewBag.BitisTarihi = bitisTarihi.Value.ToString("yyyy-MM-dd");
                }

                if (musteriId.HasValue && musteriId.Value > 0)
                {
                    query = query.Where(s => s.MusteriID == musteriId.Value);
                    ViewBag.SeciliMusteriId = musteriId.Value;
                }

                var satislar = query.OrderByDescending(s => s.SatisTarihi).ToList();

                // İstatistikler
                ViewBag.ToplamSatis = satislar.Count;
                ViewBag.ToplamTutar = satislar.Sum(s => s.Tutar);
                ViewBag.OrtalamaTutar = satislar.Any() ? satislar.Average(s => s.Tutar) : 0;

                // Müşteri dropdown için
                ViewBag.Musteriler = new SelectList(db.Musteriler.OrderBy(m => m.adsoyad), "ID", "adsoyad", musteriId);

                return View(satislar);
            }
            catch (Exception ex)
            {
                TempData["RaporHata"] = "Rapor oluşturulurken bir hata oluştu: " + ex.Message;
                ViewBag.Musteriler = new SelectList(db.Musteriler.OrderBy(m => m.adsoyad), "ID", "adsoyad");
                return View(new List<Satislar>());
            }
        }

        // Gider Raporları
        public ActionResult GiderRaporlari()
        {
            ViewBag.PageHeader = "Gider Raporları";
            return View();
        }

        [HttpPost]
        public ActionResult GiderRaporlari(DateTime? baslangicTarihi, DateTime? bitisTarihi, string kategori)
        {
            try
            {
                ViewBag.PageHeader = "Gider Raporları";

                var query = db.Giderler.AsQueryable();

                if (baslangicTarihi.HasValue)
                {
                    query = query.Where(g => g.Tarih >= baslangicTarihi.Value);
                    ViewBag.BaslangicTarihi = baslangicTarihi.Value.ToString("yyyy-MM-dd");
                }

                if (bitisTarihi.HasValue)
                {
                    query = query.Where(g => g.Tarih <= bitisTarihi.Value);
                    ViewBag.BitisTarihi = bitisTarihi.Value.ToString("yyyy-MM-dd");
                }

                if (!string.IsNullOrEmpty(kategori))
                {
                    query = query.Where(g => g.Konu.Contains(kategori));
                    ViewBag.SeciliKategori = kategori;
                }

                var giderler = query.OrderByDescending(g => g.Tarih).ToList();

                // İstatistikler
                ViewBag.ToplamGider = giderler.Count;
                ViewBag.ToplamTutar = giderler.Where(g => g.Tutar.HasValue).Sum(g => g.Tutar.Value);
                ViewBag.OrtalamaTutar = giderler.Where(g => g.Tutar.HasValue).Any() ?
                    giderler.Where(g => g.Tutar.HasValue).Average(g => g.Tutar.Value) : 0;

                // Kategori dropdown için
                var kategoriler = db.Giderler.Where(g => !string.IsNullOrEmpty(g.Konu))
                    .Select(g => g.Konu).Distinct().OrderBy(k => k).ToList();
                ViewBag.Kategoriler = new SelectList(kategoriler, kategori);

                return View(giderler);
            }
            catch (Exception ex)
            {
                TempData["RaporHata"] = "Rapor oluşturulurken bir hata oluştu: " + ex.Message;
                return View(new List<Giderler>());
            }
        }

        // Müşteri Raporları
        public ActionResult MusteriRaporlari()
        {
            try
            {
                ViewBag.PageHeader = "Müşteri Raporları";

                var musteriler = db.Musteriler
                    .Include(m => m.City)
                    .Include(m => m.Town)
                    .Include(m => m.Neighborhood)
                    .ToList();

                var musteriRaporlari = musteriler.Select(m => new MusteriRaporViewModel
                {
                    Musteri = m,
                    ToplamSatisSayisi = db.Satislar.Count(s => s.MusteriID == m.ID),
                    ToplamSatisTutari = db.Satislar.Where(s => s.MusteriID == m.ID).Sum(s => (decimal?)s.Tutar) ?? 0,
                    SonSatisTarihi = db.Satislar.Where(s => s.MusteriID == m.ID)
                        .OrderByDescending(s => s.SatisTarihi)
                        .Select(s => (DateTime?)s.SatisTarihi)
                        .FirstOrDefault()
                }).OrderByDescending(mr => mr.ToplamSatisTutari).ToList();

                return View(musteriRaporlari);
            }
            catch (Exception ex)
            {
                TempData["RaporHata"] = "Müşteri raporu oluşturulurken bir hata oluştu: " + ex.Message;
                return View(new List<MusteriRaporViewModel>());
            }
        }

        // Ürün Raporları
        public ActionResult UrunRaporlari()
        {
            try
            {
                ViewBag.PageHeader = "Ürün Raporları";

                var urunler = db.Urun.ToList();

                var urunRaporlari = urunler.Select(u => new UrunRaporViewModel
                {
                    Urun = u,
                    ToplamSatisSayisi = db.Satislar.Count(s => s.UrunID == u.UrunID),
                    ToplamSatisTutari = db.Satislar.Where(s => s.UrunID == u.UrunID).Sum(s => (decimal?)s.Tutar) ?? 0,
                    ToplamSatisMiktari = db.Satislar.Where(s => s.UrunID == u.UrunID).Sum(s => (int?)s.UrunAdeti) ?? 0
                }).OrderByDescending(ur => ur.ToplamSatisTutari).ToList();

                return View(urunRaporlari);
            }
            catch (Exception ex)
            {
                TempData["RaporHata"] = "Ürün raporu oluşturulurken bir hata oluştu: " + ex.Message;
                return View(new List<UrunRaporViewModel>());
            }
        }

        // Biçme Raporları
        public ActionResult BicmeRaporlari()
        {
            try
            {
                ViewBag.PageHeader = "Biçme İşleri Raporları";

                var bicmeler = db.BicilenTarlalar
                    .Include(b => b.Musteri)
                    .Include(b => b.Musteri.Town)
                    .Include(b => b.Musteri.Neighborhood)
                    .OrderByDescending(b => b.BicimTarihi)
                    .ToList();

                // İstatistikler
                ViewBag.ToplamTarla = bicmeler.Count;
                ViewBag.ToplamDonum = bicmeler.Sum(b => b.Donum);
                ViewBag.ToplamTutar = bicmeler.Sum(b => b.ToplamTutar);
                ViewBag.ToplamTahsilat = bicmeler.Sum(b => b.TahsilatTutari);
                ViewBag.KalanBorc = bicmeler.Sum(b => b.KalanTutar);

                return View(bicmeler);
            }
            catch (Exception ex)
            {
                TempData["RaporHata"] = "Biçme raporu oluşturulurken bir hata oluştu: " + ex.Message;
                return View(new List<BicilenTarlalar>());
            }
        }

        // Araç Raporları
        public ActionResult AracRaporlari()
        {
            try
            {
                ViewBag.PageHeader = "Araç ve Yakıt Raporları";

                var araclar = db.Araclar.Include(a => a.Yakitlar).ToList();

                var aracRaporlari = araclar.Select(a => new AracRaporViewModel
                {
                    Arac = a,
                    ToplamYakitTutari = a.Yakitlar.Sum(y => (decimal?)y.YakitTutari) ?? 0,
                    ToplamYakitLitre = a.Yakitlar.Sum(y => (decimal?)y.Litre) ?? 0,
                    YakitAlimSayisi = a.Yakitlar.Count(),
                    SonYakitTarihi = a.Yakitlar.OrderByDescending(y => y.AlisTarihi)
                        .Select(y => (DateTime?)y.AlisTarihi).FirstOrDefault()
                }).OrderByDescending(ar => ar.ToplamYakitTutari).ToList();

                return View(aracRaporlari);
            }
            catch (Exception ex)
            {
                TempData["RaporHata"] = "Araç raporu oluşturulurken bir hata oluştu: " + ex.Message;
                return View(new List<AracRaporViewModel>());
            }
        }

        // Aylık Özet Raporu
        public ActionResult AylikOzet(int? yil, int? ay)
        {
            try
            {
                ViewBag.PageHeader = "Aylık Özet Raporu";

                var secilenYil = yil ?? DateTime.Now.Year;
                var secilenAy = ay ?? DateTime.Now.Month;

                ViewBag.SecilenYil = secilenYil;
                ViewBag.SecilenAy = secilenAy;
                ViewBag.AyAdi = CultureInfo.GetCultureInfo("tr-TR").DateTimeFormat.GetMonthName(secilenAy);

                var baslangicTarihi = new DateTime(secilenYil, secilenAy, 1);
                var bitisTarihi = baslangicTarihi.AddMonths(1).AddDays(-1);

                var aylikOzet = new AylikOzetViewModel
                {
                    Yil = secilenYil,
                    Ay = secilenAy,

                    // Satış verileri
                    ToplamSatis = db.Satislar.Count(s => s.SatisTarihi >= baslangicTarihi && s.SatisTarihi <= bitisTarihi),
                    ToplamSatisGeliri = db.Satislar.Where(s => s.SatisTarihi >= baslangicTarihi && s.SatisTarihi <= bitisTarihi)
                        .Sum(s => (decimal?)s.Tutar) ?? 0,

                    // Gider verileri
                    ToplamGider = db.Giderler.Count(g => g.Tarih >= baslangicTarihi && g.Tarih <= bitisTarihi),
                    ToplamGiderTutari = db.Giderler.Where(g => g.Tarih >= baslangicTarihi && g.Tarih <= bitisTarihi && g.Tutar.HasValue)
                        .Sum(g => g.Tutar.Value),

                    // Biçme verileri
                    ToplamBicme = db.BicilenTarlalar.Count(b => b.BicimTarihi >= baslangicTarihi && b.BicimTarihi <= bitisTarihi),
                    ToplamBicmeGeliri = db.BicilenTarlalar.Where(b => b.BicimTarihi >= baslangicTarihi && b.BicimTarihi <= bitisTarihi)
                        .Sum(b => (decimal?)b.ToplamTutar) ?? 0,

                    // Yakıt verileri
                    ToplamYakitGideri = db.Yakitlar.Where(y => y.AlisTarihi >= baslangicTarihi && y.AlisTarihi <= bitisTarihi)
                        .Sum(y => (decimal?)y.YakitTutari) ?? 0
                };

                aylikOzet.NetKar = aylikOzet.ToplamSatisGeliri + aylikOzet.ToplamBicmeGeliri - aylikOzet.ToplamGiderTutari - aylikOzet.ToplamYakitGideri;

                // Yıl ve ay dropdown'ları için
                ViewBag.Yillar = Enumerable.Range(2020, DateTime.Now.Year - 2019).Select(y => new SelectListItem
                {
                    Value = y.ToString(),
                    Text = y.ToString(),
                    Selected = y == secilenYil
                }).ToList();

                ViewBag.Aylar = Enumerable.Range(1, 12).Select(m => new SelectListItem
                {
                    Value = m.ToString(),
                    Text = CultureInfo.GetCultureInfo("tr-TR").DateTimeFormat.GetMonthName(m),
                    Selected = m == secilenAy
                }).ToList();

                return View(aylikOzet);
            }
            catch (Exception ex)
            {
                TempData["RaporHata"] = "Aylık özet raporu oluşturulurken bir hata oluştu: " + ex.Message;
                return View(new AylikOzetViewModel());
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }

    // ViewModel'lar
    public class MusteriRaporViewModel
    {
        public Musteriler Musteri { get; set; }
        public int ToplamSatisSayisi { get; set; }
        public decimal ToplamSatisTutari { get; set; }
        public DateTime? SonSatisTarihi { get; set; }
    }

    public class UrunRaporViewModel
    {
        public Urun Urun { get; set; }
        public int ToplamSatisSayisi { get; set; }
        public decimal ToplamSatisTutari { get; set; }
        public int ToplamSatisMiktari { get; set; }
    }

    public class AracRaporViewModel
    {
        public Araclar Arac { get; set; }
        public decimal ToplamYakitTutari { get; set; }
        public decimal ToplamYakitLitre { get; set; }
        public int YakitAlimSayisi { get; set; }
        public DateTime? SonYakitTarihi { get; set; }
    }

    public class AylikOzetViewModel
    {
        public int Yil { get; set; }
        public int Ay { get; set; }
        public int ToplamSatis { get; set; }
        public decimal ToplamSatisGeliri { get; set; }
        public int ToplamGider { get; set; }
        public decimal ToplamGiderTutari { get; set; }
        public int ToplamBicme { get; set; }
        public decimal ToplamBicmeGeliri { get; set; }
        public decimal ToplamYakitGideri { get; set; }
        public decimal NetKar { get; set; }
    }
}
