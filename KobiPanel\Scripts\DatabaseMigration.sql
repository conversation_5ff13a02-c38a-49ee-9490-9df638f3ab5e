-- KobiPanel Database Migration Script
-- Bu script yeni eklenen modeller için gerekli tabloları oluşturur

-- Kullanicilar tablosuna yeni kolonlar ekleme
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Kullanicilar' AND COLUMN_NAME = 'Email')
BEGIN
    ALTER TABLE Kullanicilar ADD Email NVARCHAR(100) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Kullanicilar' AND COLUMN_NAME = 'Telefon')
BEGIN
    ALTER TABLE Kullanicilar ADD Telefon NVARCHAR(15) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Kullanicilar' AND COLUMN_NAME = 'Rol')
BEGIN
    ALTER TABLE Kullanicilar ADD Rol NVARCHAR(50) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Kullanicilar' AND COLUMN_NAME = 'Aktif')
BEGIN
    ALTER TABLE Kullanicilar ADD Aktif BIT DEFAULT 1;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Kullanicilar' AND COLUMN_NAME = 'SonGirisTarihi')
BEGIN
    ALTER TABLE Kullanicilar ADD SonGirisTarihi DATETIME NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Kullanicilar' AND COLUMN_NAME = 'KayitTarihi')
BEGIN
    ALTER TABLE Kullanicilar ADD KayitTarihi DATETIME DEFAULT GETDATE();
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Kullanicilar' AND COLUMN_NAME = 'GuncellemeTarihi')
BEGIN
    ALTER TABLE Kullanicilar ADD GuncellemeTarihi DATETIME NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Kullanicilar' AND COLUMN_NAME = 'ProfilResmi')
BEGIN
    ALTER TABLE Kullanicilar ADD ProfilResmi NVARCHAR(255) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Kullanicilar' AND COLUMN_NAME = 'Aciklama')
BEGIN
    ALTER TABLE Kullanicilar ADD Aciklama NVARCHAR(500) NULL;
END

-- Araclar tablosuna yeni kolonlar ekleme
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Araclar' AND COLUMN_NAME = 'Marka')
BEGIN
    ALTER TABLE Araclar ADD Marka NVARCHAR(50) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Araclar' AND COLUMN_NAME = 'Model')
BEGIN
    ALTER TABLE Araclar ADD Model NVARCHAR(50) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Araclar' AND COLUMN_NAME = 'ModelYili')
BEGIN
    ALTER TABLE Araclar ADD ModelYili INT NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Araclar' AND COLUMN_NAME = 'Aciklama')
BEGIN
    ALTER TABLE Araclar ADD Aciklama NVARCHAR(500) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Araclar' AND COLUMN_NAME = 'KayitTarihi')
BEGIN
    ALTER TABLE Araclar ADD KayitTarihi DATETIME DEFAULT GETDATE();
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Araclar' AND COLUMN_NAME = 'Aktif')
BEGIN
    ALTER TABLE Araclar ADD Aktif BIT DEFAULT 1;
END

-- AlinanYakitlar tablosuna yeni kolonlar ekleme
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'AlinanYakitlar' AND COLUMN_NAME = 'LitreBasinaFiyat')
BEGIN
    ALTER TABLE AlinanYakitlar ADD LitreBasinaFiyat DECIMAL(18,2) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'AlinanYakitlar' AND COLUMN_NAME = 'Aciklama')
BEGIN
    ALTER TABLE AlinanYakitlar ADD Aciklama NVARCHAR(500) NULL;
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'AlinanYakitlar' AND COLUMN_NAME = 'KayitTarihi')
BEGIN
    ALTER TABLE AlinanYakitlar ADD KayitTarihi DATETIME DEFAULT GETDATE();
END

-- Veri tiplerini güncelle
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'AlinanYakitlar' AND COLUMN_NAME = 'YakitTutari' AND DATA_TYPE = 'int')
BEGIN
    ALTER TABLE AlinanYakitlar ALTER COLUMN YakitTutari DECIMAL(18,2);
END

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'AlinanYakitlar' AND COLUMN_NAME = 'Litre' AND DATA_TYPE = 'int')
BEGIN
    ALTER TABLE AlinanYakitlar ALTER COLUMN Litre DECIMAL(18,2);
END

-- Bildirimler tablosu oluşturma
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Bildirimler')
BEGIN
    CREATE TABLE Bildirimler (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        Baslik NVARCHAR(200) NOT NULL,
        Mesaj NVARCHAR(1000) NOT NULL,
        Tip NVARCHAR(50) NOT NULL,
        KullaniciId INT NULL,
        Okundu BIT DEFAULT 0,
        OlusturmaTarihi DATETIME DEFAULT GETDATE(),
        OkunmaTarihi DATETIME NULL,
        Url NVARCHAR(500) NULL,
        Ikon NVARCHAR(50) NULL,
        Oncelik INT DEFAULT 2,
        Aktif BIT DEFAULT 1,
        FOREIGN KEY (KullaniciId) REFERENCES Kullanicilar(Id)
    );
END

-- KantarFisi tablosuna MusteriID kolonu ekleme (eğer yoksa)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'KantarFisi' AND COLUMN_NAME = 'MusteriID')
BEGIN
    ALTER TABLE KantarFisi ADD MusteriID INT NULL;
END

-- Varsayılan değerleri güncelle
UPDATE Kullanicilar SET Aktif = 1 WHERE Aktif IS NULL;
UPDATE Kullanicilar SET KayitTarihi = GETDATE() WHERE KayitTarihi IS NULL;
UPDATE Kullanicilar SET Rol = 'Kullanici' WHERE Rol IS NULL;

UPDATE Araclar SET Aktif = 1 WHERE Aktif IS NULL;
UPDATE Araclar SET KayitTarihi = GETDATE() WHERE KayitTarihi IS NULL;

UPDATE AlinanYakitlar SET KayitTarihi = GETDATE() WHERE KayitTarihi IS NULL;

-- İndeksler oluşturma
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Bildirimler_KullaniciId')
BEGIN
    CREATE INDEX IX_Bildirimler_KullaniciId ON Bildirimler(KullaniciId);
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Bildirimler_Okundu')
BEGIN
    CREATE INDEX IX_Bildirimler_Okundu ON Bildirimler(Okundu);
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Bildirimler_OlusturmaTarihi')
BEGIN
    CREATE INDEX IX_Bildirimler_OlusturmaTarihi ON Bildirimler(OlusturmaTarihi);
END

-- Örnek bildirimler ekleme
IF NOT EXISTS (SELECT * FROM Bildirimler WHERE Baslik = 'Sistem Güncellendi')
BEGIN
    INSERT INTO Bildirimler (Baslik, Mesaj, Tip, Ikon, Oncelik, Aktif, OlusturmaTarihi)
    VALUES ('Sistem Güncellendi', 'KobiPanel sistemi başarıyla güncellendi. Yeni özellikler ve iyileştirmeler eklendi.', 'Sistem', 'fas fa-cog', 2, 1, GETDATE());
END

IF NOT EXISTS (SELECT * FROM Bildirimler WHERE Baslik = 'Yeni Özellikler')
BEGIN
    INSERT INTO Bildirimler (Baslik, Mesaj, Tip, Ikon, Oncelik, Aktif, OlusturmaTarihi)
    VALUES ('Yeni Özellikler', 'Kullanıcı yönetimi, raporlama sistemi ve dark/light tema özellikleri eklendi.', 'Bilgi', 'fas fa-info-circle', 2, 1, GETDATE());
END

-- Varsayılan admin kullanıcısı oluşturma (eğer yoksa)
IF NOT EXISTS (SELECT * FROM Kullanicilar WHERE KullaniciAdi = 'admin')
BEGIN
    INSERT INTO Kullanicilar (KullaniciAdi, Sifre, AdSoyad, Email, Rol, Aktif, KayitTarihi)
    VALUES ('admin', 'jGl25bVBBBW96Qi9Te4V37Fnqchz/Eu4qB9vKrRIqRg=', 'Sistem Yöneticisi', '<EMAIL>', 'Yonetici', 1, GETDATE());
    -- Şifre: admin123 (SHA256 hash)
END

PRINT 'Database migration completed successfully!';
