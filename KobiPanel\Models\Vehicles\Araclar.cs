namespace KobiPanel.Models.Vehicles
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("Araclar")]
    public class Araclar
    {
        public Araclar()
        {
            Yakitlar = new HashSet<AlinanYakitlar>();
        }

        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        [Required(ErrorMessage = "Plaka alanı zorunludur.")]
        [StringLength(9, ErrorMessage = "Plaka en fazla 9 karakter olabilir.")]
        [DisplayName("Araç Plakası")]
        public string Plaka { get; set; }

        [DisplayName("Araç Markası")]
        [StringLength(50)]
        public string Marka { get; set; }

        [DisplayName("Araç Modeli")]
        [StringLength(50)]
        public string Model { get; set; }

        [DisplayName("Model Yılı")]
        public int? ModelYili { get; set; }

        [DisplayName("Vize Bitiş Tarihi")]
        [DataType(DataType.Date)]
        public DateTime? VizeBitisTarihi { get; set; }

        [DisplayName("Sigorta Bitiş Tarihi")]
        [DataType(DataType.Date)]
        public DateTime? SigortaBitisTarihi { get; set; }

        [DisplayName("Egzoz Muayenesi Bitiş Tarihi")]
        [DataType(DataType.Date)]
        public DateTime? EgzozBitisTarihi { get; set; }

        [DisplayName("Tescil Numarası")]
        [StringLength(20)]
        public string TescilNo { get; set; }

        [DisplayName("Açıklama")]
        [StringLength(500)]
        public string Aciklama { get; set; }

        [DisplayName("Kayıt Tarihi")]
        [DataType(DataType.DateTime)]
        public DateTime KayitTarihi { get; set; }

        [DisplayName("Aktif")]
        public bool Aktif { get; set; }

        // Navigation Properties
        public virtual ICollection<AlinanYakitlar> Yakitlar { get; set; }
    }
}
