﻿<html lang="tr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Kobi Panel - @ViewBag.Title</title>
    <!-- Font Awesome -->
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css">
    <!-- Bootstrap core CSS -->
    <link href="~/Content/Mdb/css/bootstrap.min.css" rel="stylesheet" />
    <!-- Material Design Bootstrap -->
    <link href="~/Content/Mdb/css/mdb.min.css" rel="stylesheet" />
    <link href="~/Content/ism.css" rel="stylesheet" />
    <!-- Dark Theme CSS -->
    <link href="~/Content/css/dark-theme.css" rel="stylesheet" />
    <script src="~/Content/Mdb/js/jquery-3.3.1.min.js"></script>
    <script src="~/Scripts/jquery.validate.min.js"></script>
    <script src="~/Scripts/jquery.validate.unobtrusive.min.js"></script>
    <!-- Your custom styles (optional) -->
</head>
<body class="white-skin">
    <!--Main Navigation-->
    @Html.Partial("_Navbar")
    <!--Main Navigation-->
    <!--Main layout-->
    <main style="margin-top: 80px;">
        <div class="container-fluid">
            <section class="mb-5">
                <!--Card-->
                <div class="card card-cascade">
                    <!--Section: Content-->
                    <section>
                        <!--Card image-->
                        <div class="view view-cascade gradient-card-header light-blue lighten-1">
                            <h2 class="h2-responsive mb-0 p-3">@ViewBag.PageHeader</h2>
                        </div>
                        <!--/Card image-->
                        <!--Card content-->
                        <div class="card-body card-body-cascade">
                            @RenderBody()
                        </div>
                        <!--/.Card content-->
                    </section>
                </div>
            </section>
        </div>
    </main>
    <!--Main layout-->
    <!--Footer-->
    @Html.Partial("_Footer")
    <!--/.Footer-->
    <!-- SCRIPTS -->
    <!-- JQuery -->
    <!-- Bootstrap tooltips -->
    <script src="~/Content/Mdb/js/popper.min.js"></script>
    <!-- Bootstrap core JavaScript -->
    <script src="~/Content/Mdb/js/bootstrap.min.js"></script>
    <!-- MDB core JavaScript -->
    <script src="~/Content/Mdb/js/mdb.min.js"></script>
    <!-- Theme Toggle JavaScript -->
    <script src="~/Scripts/theme-toggle.js"></script>
    <!--Initializations-->
    <script>
        // Data Picker Initialization
        $('.datepicker').pickadate();

        // Material Select Initialization
        $(document).ready(function () {
            $('.mdb-select').materialSelect({
                destroy: true
            });
        });

        // Tooltips Initialization
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        });

        // Navbar dropdown hover effect (optional)
        $('.navbar-nav .dropdown').hover(function() {
            $(this).find('.dropdown-menu').stop(true, true).delay(200).fadeIn(300);
        }, function() {
            $(this).find('.dropdown-menu').stop(true, true).delay(200).fadeOut(300);
        });

        // Set active navbar item based on current page
        function setActiveNavItem() {
            var currentPath = window.location.pathname.toLowerCase();

            // Remove all active classes
            $('.navbar-nav .nav-item').removeClass('active');

            // Add active class based on current path
            if (currentPath.includes('/home') || currentPath === '/' || currentPath === '') {
                $('a[href*="Home/Index"]').closest('.nav-item').addClass('active');
            } else if (currentPath.includes('/musteri')) {
                $('a[href*="MusteriAnasayfa"]').closest('.nav-item').addClass('active');
            } else if (currentPath.includes('/satis')) {
                $('a[href*="SatisAnasayfa"]').closest('.nav-item').addClass('active');
            } else if (currentPath.includes('/gider')) {
                $('a[href*="GiderAnasayfa"]').closest('.nav-item').addClass('active');
            } else if (currentPath.includes('/urun')) {
                $('a[href*="UrunAnasayfa"]').closest('.nav-item').addClass('active');
            } else if (currentPath.includes('/arac') || currentPath.includes('/yakit')) {
                $('#araclarDropdown').closest('.nav-item').addClass('active');
            } else if (currentPath.includes('/bicme') || currentPath.includes('/tarla') || currentPath.includes('/kantar')) {
                $('#bicmeDropdown').closest('.nav-item').addClass('active');
            } else if (currentPath.includes('/rapor')) {
                $('a[href*="Rapor"]').closest('.nav-item').addClass('active');
            } else if (currentPath.includes('/kullanici') || currentPath.includes('/yonetim')) {
                $('#yonetimDropdown').closest('.nav-item').addClass('active');
            }
        }

        // Call on page load
        setActiveNavItem();

    </script>
    <div class="drag-target" style="left: 0px;"></div>



    <div class="ff-ext--bootstrapResponsiveHelper">
        <div class="visible-lg-block" attr-tag="lg" attr-version="3"></div><div class="visible-md-block" attr-tag="md" attr-version="3"></div><div class="visible-sm-block" attr-tag="sm" attr-version="3"></div><div class="visible-xs-block" attr-tag="xs" attr-version="3"></div><div class="d-none d-xl-block" attr-tag="xl" attr-version="4"></div><div class="d-none d-lg-block" attr-tag="lg" attr-version="4"></div><div class="d-none d-md-block" attr-tag="md" attr-version="4"></div><div class="d-none d-sm-block" attr-tag="sm" attr-version="4"></div><div class="d-none d-block" attr-tag="xs" attr-version="4"></div>
    </div>
</body>
</html>