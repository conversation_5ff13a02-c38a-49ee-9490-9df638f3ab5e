﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace KobiPanel.Models
{
    [Table("Kullanicilar")]
    public class Kullanicilar
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required(ErrorMessage = "Kullanıcı adı zorunludur.")]
        [StringLength(50, ErrorMessage = "Kullanıcı adı en fazla 50 karakter olabilir.")]
        [DisplayName("Kullanıcı Adı")]
        public string KullaniciAdi { get; set; }

        [Required(ErrorMessage = "Şifre zorunludur.")]
        [StringLength(255, ErrorMessage = "Şifre en fazla 255 karakter olabilir.")]
        [DisplayName("Şifre")]
        public string Sifre { get; set; }

        [Required(ErrorMessage = "Ad Soyad zorunludur.")]
        [StringLength(100, ErrorMessage = "Ad Soyad en fazla 100 karakter olabilir.")]
        [DisplayName("Ad Soyad")]
        public string AdSoyad { get; set; }

        [Required(ErrorMessage = "E-posta adresi zorunludur.")]
        [EmailAddress(ErrorMessage = "Geçerli bir e-posta adresi giriniz.")]
        [StringLength(100, ErrorMessage = "E-posta adresi en fazla 100 karakter olabilir.")]
        [DisplayName("E-posta")]
        public string Email { get; set; }

        [StringLength(15, ErrorMessage = "Telefon numarası en fazla 15 karakter olabilir.")]
        [DisplayName("Telefon")]
        public string Telefon { get; set; }

        [Required(ErrorMessage = "Rol seçimi zorunludur.")]
        [DisplayName("Rol")]
        public string Rol { get; set; }

        [DisplayName("Aktif")]
        public bool Aktif { get; set; }

        [DisplayName("Son Giriş Tarihi")]
        [DataType(DataType.DateTime)]
        public DateTime? SonGirisTarihi { get; set; }

        [DisplayName("Kayıt Tarihi")]
        [DataType(DataType.DateTime)]
        public DateTime KayitTarihi { get; set; }

        [DisplayName("Güncelleme Tarihi")]
        [DataType(DataType.DateTime)]
        public DateTime? GuncellemeTarihi { get; set; }

        [DisplayName("Profil Resmi")]
        [StringLength(255)]
        public string ProfilResmi { get; set; }

        [DisplayName("Açıklama")]
        [StringLength(500)]
        public string Aciklama { get; set; }

        // Şifre doğrulama için kullanılacak (veritabanına kaydedilmez)
        [NotMapped]
        [Required(ErrorMessage = "Şifre tekrarı zorunludur.")]
        [Compare("Sifre", ErrorMessage = "Şifreler eşleşmiyor.")]
        [DisplayName("Şifre Tekrarı")]
        public string SifreTekrar { get; set; }
    }

    // Kullanıcı rolleri için enum
    public enum KullaniciRolleri
    {
        [Display(Name = "Yönetici")]
        Yonetici,
        [Display(Name = "Kullanıcı")]
        Kullanici,
        [Display(Name = "Misafir")]
        Misafir
    }
}