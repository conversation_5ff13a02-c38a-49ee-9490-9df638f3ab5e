﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>H4sIAAAAAAAEAO1d3W7cOJa+X2DfoVBXu4MeV+wgi+nAnoFjJz2B24kRJ43tvQnoKtrWlkqqkVSZuBfzZHOxj7SvsJQoiTz8p0ipKkHDNy7+fPw7PIc8PEfn//75v6d/+bpJZ19wUSZ5djY/Pno2n+Fsma+S7OFsvqvu//in+V/+/K//cvp6tfk6+6Ur97wuR2pm5dn8saq2LxeLcvmIN6g82iTLIi/z++pomW8WaJUvTp49+3FxfLzABGJOsGaz0w+7rEo2uPlBfl7k2RJvqx1Kr/MVTss2neTcNqizd2iDyy1a4rP5VX6X3KAMp0e07Hx2niaI9OMWp/fzGcqyvEIV6eXLTyW+rYo8e7jdkgSUfnzaYlLuHqUlbnv/khV3Hcizk3ogC1axg1ruyirfeAIeP29nZiFWHzS/837myNy9JnNcPdWjbubvbH5eoGWKivlMbOvlRVrU5eTZPfoFPybLFJdHbeUfZn2RH3p6IGRT//0wu9il1a7AZxneVQUiJW52d2myvMJPH/M1zs6yXZryfSS9JHkggSTdFPkWF9XTB3zf9vzt5Xy2gPUWYsW+GleHDuptVj0/mc/ekcbRXYp7EuAm4LbKC/wTznCBKry6QVWFC7KCb1e4mUSpdaGtmxStUdccITqye+aza/T1Z5w9VI9n8x/nszfJV7zqfrcd+JQlZKuRKlWxw4oOmhu9RoWx0RfPnFq1NFKTwSSN/JqkibBi5lq/JL/hV0mVlB9RkTz2dS/JCn4kzMVW/TZ5yIsKBSC8fvgt/y2g/kdcLpP0XW6Y3pMY03u+TNYp2phpJUZDV+gpqXRz4Und5+sque9QXuV5ilFmB3mHviQPzZYW4H5F66RqmN8HnDYFysdkS6XGEZEgGcq6Ip9rVkdmo8g3H/K6KUX2ZzLKB1yR/uX6Mrf5rlgKfTxdMLZsZtYAcCDPBhi/s24lnZGVsrdnxmhm+OOuIpTf0z1eJhtEOOdNQf5rT1d/ms9ul6iGHdhGktVLukoekl9xYdjOx47bOawPV9de/JpU0zJKz478nFQFHm2qG/RXqCSb503yhCr3hr4HPqxloZQtxmGfHWs0sc+OxQ5in68SIlpxRjBS5Ms/Sd0NPhIQ9sM9m9aHsNC+4lR89JrMLy6SUFZ6mWe7TRhEM/TzVWLYZuTfCNusoZOGQ4gHV98e51vCFxoBEjr0xzIh+xPKomFYV4Tqswh9qvfRJhLfR3YOGmdp6Yr8FZWouk7W4ZPZkDUVKVcPnlBahrxGGenZPZFESrZ81WS/IdmfJW7IuLO+lHTGNRRVHXVNXW+5hbLfInZflnVaU0QSK7pyQaKFTcMAqcIq70egkJaHiJO22lTCxFF6TSKR7hLCY5YJ6VKVBEqmZB0L6R2uvPmIp87KlZF6Nnub3+dFHOl8sMKjWeDo52+JiY/B80UO6iAeBjHRdnOm2O9ozqr9rs1Q0viqzJ/QavrNVRm1xCM1uo6ob7zc/YazFG9wJLwnVHLLfvwftvIXZHUlSrEdUv+eeVZ5h5OHx7u8eMzzlWfVq91ytyZH2L+ipy8ouyVTX/rp61/tnjwBtKzwotkKCv7H+MNnWoYxPSFL4nRivoq9mfrET62tb7Csso98EVNfQTnfPtcUZOsrLaPsY51l6luTHyQm6CJ6CIi6wn5EQ7eBfcWDcuOPKCIu8l1WFQ5N2vlV/d/opylyTq2w8ZEsUjuPeYYvSPHILTnTOmQKHjTPV9wP7YtixXcPGMXSiHuBb3cSYr5MSgK9rEJ3338l2xFI1SQqup4rxQUQQqwkExrKApLoUJcKEiCsMx4bqqu0n83E04jvRnKnr4jKGtUx1Pf03fZ7nE3of/5hxCecfkCGRMAwN4hwacMeRFtX2NMjVUsA3m9UToQT8eTjdNKyE/vERKq97DREJl5z+kSJOFlO4MtqukqKWoHppfymlfaktVkN0NqspiNMcilOk7XRDGwUe45rXKL/NpuRjNLux2Q7vbLoqi5OEBJpZc313q932WrnbYkmoqRks+yKDYqkXqp7pQcz1/1UmC06Y5igvF3XJszjmo2+z5aY2zfDGHpkO8OeyjRPm5QNfuaK8a+aYq7iQVMqEvaW2cH42sjwFX9n6kZ+Y37sctwJvq9syX1hOqOcvHgxRrPnq1vL88NIlomvNygxMbWRmv2IU3xvZHTHbvNsbobs/enl5TDGKL32Zj8RfjXQQD6mPflPu1pamB54LOrBIr9P0g+43Jh2s+u+2ouhqLNU+ClZ4cL3abartB9pkAyQBsmU0iDPdoblfD4Oe+Kt96KbSTf7yHNfOpPgz/mDifrOy+07XF1/Wb5JUrIQZUeEpNrvp5FYizX4THMyzuFiWU+ERfniKuhtb2RZVeREWhSTNEdO9Q/m+YxyJbsZ/XnjGlePeQzrk2EPaLmfmp+U/11Yqdpam4XV4dvnHZIkuyVLUvper7tK+6HPpvUhGv2+4lSU+qnYBb89RbITrrtyvsLBviCjntlqx72CGR9Hx7/FxZekvE5C741XBV4lV7UdbThWQ5SRbpHv0DpNnvArTHZp6ELjMk02r1e1Ri98kBRt2NXW4boZ+02LN/9V6Ek79veZL8cUpYpsSVOqKuNrHldvaXP/aAlFz+oMfZ+a3CDFLW3YQ6LUFfYjTToe7StM3Hh7XFmyF8eEumHoRhidL1/jL8tdRSYo8LWk5X+flkWwoHu9TnEWzdy6Nd8eCqjbd+dlmS+Thrw6ZqlwdoY9e52tZg6ez5TIZP9pQnFkIyZbsvVIh87mf5CGbm6g99rjGug+/AORjyVksmlxUe8WlJIrcEnYQJJV8g6vvZi2KLV3Qqiq1pxIrKFejr4RMecSb3FW72j7HLu03n3+Qe5B35DAtmxzdLrgiMZMSwZvHd2Ku7jusIXnXRXdqcrFJZS1IfkZjUJk9j65rLbGnd6L4OwrENiTCQhPdJPQUYLWZ4ItP39CcycxnbMFB0wNmyDks6OjSPSk6YDL0qkN/b1oSDOvYa1PQDfM4ky3sArzM7ak1KbRnUpkizULfRyLU3j6PrvEKa7wjGqPyfyhcolW8nmCHABWEehK6vJEFCXN+8HTEjSx1ZGAxt6WkQEz8HanK7WZroVQ909bym47CRulga4XfSnXIaztCWhM7ZegIwuLkwIjD+ip4053Zv8GB6LePw0ah+BCD3rvBi96NK5VeD+mPYMBenI4Mql9QyOeyZROpY4bYJQzmqpDLqtsdkwbemZTzX+c3kxLd0bJq/XzjUhnPvJ3FLragyTVzOvBy1LdZ5l0y2z9RpOzCsFETLYvPDlRayS6svRlAmWYZc5desC9hu6JzCQTef3a6+3leeLq/JV8qEprZs9r14Dx/FjcSt8XJ4KSTE08CUo3xS6NA5ebvVCT6iFRt+jGV0W26syew52eTI+Rjhxq3wd/wxAmYGyGtfkWmBp8L7ZSCXw8jkR64M2ZA6UvyQdLbny3XZZa/do8jNj4dQhrOzqN0efK2k6V1MBFb2XePu9f3tVZ+KvKIPJTiVszgbJ9sBZpp4a+xRX3RNYQHXsiFd8VJeqDCOxpU4YQ3j4tSNJJUQKUSlgQ+ScyCYzPtODwvFvC4TMtOFStLSFcqAwYxLrwWi5hwGwLFlNBSTgsy4JBr3FSfZpsXWl68lBPKDvU2ZYXnNHkBQbZFizmFiPhsCwLRuPXIFVvUm2Lm6vWokm11GRiQ6rOsiwYVEpI9WmyUJdjWRwXUX0knitp+Ja8yE3dDCz6EfD8R+LMbrYUHJaO5y3gqB1mxPStTnliXG0FwJgcrAW4oem5nSuoYr5s/Nh/3qRv/MmTZXzf1imjwKslNwI98zYiKeZCycT9J4D77oc8dM0TLeiq/EjLdVLFnTW1xxui8P0deZiG10PQWfX7IddhnTAzoCiGrRRp/sPWfEdLHr7Dw5bH0xY3ENMZweMty2GCw3Y+7KeRA+hfV7zeV4I4gvJBxXHawyZKt4GMzwBODwFBEzLyRtLGEJAnwkmv7aXZdpd+Xqpsp/keNFfyR0lU02TWy7pqZsHkcEd848RotbH86cV0lPefFaULgjwtVgWjs4qRG4ruVO6sUxyLVAS/B8N0yIouB1XX4CkAui0ORXk90Q+7swLv1Sp93umCRqJtE04XmpC1p9dou02yBy6EbZsyu6Xxay/+eOsf2nVDMRbLUhHhte9t31KVF+gBC7m1V/MKv0mKsrpEFbpDtQ38xWojFVMokTR3w65BqCeS16u7NHbl6/8FhVXnpqIBYfP4hgxtU+vqGm8PxcVMqjmrwwjXHFX0h74k48/T3SbTq6j1tdvwGTxAm+SO0UZw5THaJA8MGqAVYNAkTwwaf1XCocnuWFJUVh5RynTHVYVr5aFV+e7ocihXHlvOdUdmQV55RJbqjsTc9HgkluqOBL6rw4OBDI+e0U8FgW7RJBnjdCFsZElXLvEL6YEBsh8n5tSrgMK4E9RWD2BSFoBxeFXn5AIWSOP4okcBUU55KJDhiSdENJVghfzB6HWsUgN4ne0xn6maT/Dp7mhtMFMeqE3yxAAhSyU4kPstcZ09cQy7ltWDcVjAHDiHFUErgDpPIyB/dO5HehzuDR0cEvRP63qsNsonj9MmeQjWPsynNLAm1R0JxPHkwUCGR8/4cJ6gc3yGz0hhVE84Xpjnsx1ZfE+4G1m61xyyzw8Ic8gy3PGQku2gAWxHFcpTXhSY70nJLLCnRNEs62AYG/fME8TT9DgO7MxUWTfXbRBKfo6V4SyN1HCA3FCIMMnjCVnumDDUJA8Jc9wR25CTPFSbNO2lmQWQBNe/PnUvPAZEfBRIqzLxvz3xAKO20oMH6HEceICp8jgXoT48IljwLtEdpxJVLpWfwmWtOwqvh13ApeCFQA6Jme64TRBDHqtJcK/fOZjyEDqnUz1K5/YApbbaFUKPIjrfQEZmdszRo6qjIoLzlLKEx8lKGTYRHLGUJQ6G12iMETy4jArBgb+oq41Lq1x0PQDEkv12T/2f3Cea6iV3aQw9QfLSRA8cFiMPILHkg6E6y6u/B/WZkByo0Fx9Wm4lx7vT4fpSGO9OC4SPwc1Wj9aHt+Oh+sSDoTGD4Y0HfelQHGhLX3WadYojmWHwN1WvdNS4p3XXGMx4rLkKwWG91dXGXZ14Zzh5fVnqwawtb5QSqPJtTdSH6Hp1VbXXIuFOo3K409fu4o+BQ2Wb5qESodHEgDqEJnmQSR0ZDFBIneBzHOc8DuEp3OCKqMfrgn/xUF2aB4oY/AvAiZl+vdOAghx3xCY0GA/UJLjXp2G/ACU2KR5j6oJ6geF0id/VmzSwWAtTxRqQXJSxxurj8BwYSkK5VT3VeW3MK2gUcu/3jtoHsAJ00yV62JPQeFTAiIQm+ViOtMGloOFIm+iO00SP4jGahHF2kl5pC+NAQdUtzNufHYscI4oHlXM9Ls589ChwdeYzxnyV3xOP6x3jgvibDsWBt+mrap9OhO0vR+sw1aaBngAxNike+17xcOz/ZNxEtBBeIuqkg6GM2ucxiCgUAA70oKw1jojzWYTpxCQfRgkyD5buo++EkZKg0hPm+VgTNAGRoB1Bk+RxFL0R1u7G7zJDIxnB2wxNO5gtVDv/hqk2ZQAXjaaq1jjcdC1x07UnN436yHvoPLX3mgiiCh2KA2noq2pPhl3IIHAkVAcgMuF0396Al1f19zj0KDHNOriAQGKn2uRpzwR97B+wHTPldxSMK9bH+AFL1qd6CDcQ5QcIN5Dj0Tekcw9Ag6z3hXg/4LUEZnl5BIDAP4JfAMjzRVWaoICc7/COofE18+B4KgQHbqeuNi6H6qPUyDzFU9PHgs6IUP4mr3x8GXhkYuneu66LMqPYdV2Wh0YGhpsBmhmY5W12o4GVcyfeMZI7o1ikb71N6X/37oytKyHwcWxmo/ZYbGahbN0aRd9CWqSOYJR/qa/ghAU/ESG6OaoLHN3+Lb1IEzJeVuCaXGbucVnRYFPzk2fHJ/PZeZqgkvqWtl6TL8XvWTm5UR4/r90o8WqzEKv7O2PWKGW5ArGw5ABdSndEx1jCA4JlsUBZST2p1lBYnuGVWitN2kL2BRXLx+YzdCw21o/+Mamoq6MBsw57y0CdAtBS18f4mNQNkpthPwzJ8ZEircjqVEJ0Khc4lbNjGKLs4hiGxxwcDWtx4r0WYqhCzRJ74wKFsX7kbnGqqXKcotwlVaxAsibfwW+WsXRuiFwT0gcL32Yr/PVs/j9NlZezt//5mdb6Yfa+IILj5ezZ7B/eDQOnxXbFY0fdU3kwGuj2GNLtkCZqP8bBTIp3XwzcA60D40jzKvsz+jf0bXIbZ2ZhjifnxC00XjF2ltFXHJFvSEGbXVlHXzGIe7Teisqt5hpyvPVThKT1bxv09d996Qn4KQb0iXdPDBkadEkcjsS7Ig5HAR6IgXxNjFEftGwqz8PhwxTcDV2BnDmKzmPPjZkoHfXsrOQNH05+FEaiYlYObKStFsRE1DzME0TwEBwOBP0Ch+O0ToHDAZR3TWmDuYV+770CY6DF3fzAMXCcQ4A26MG3elvoXfViLGclawqGAa0jXh4lZ71AvMZhj2KU5GicDrgWdMbefgyS1tLxRzfx+PdsAGNuaoW0K7rX+LUPa4f0Q+04OPh2p/YSdIRzZjmK4LBOzEbtUWBnOArijM10ON+94UKNee1FkB29514MLOa7543mTBWGAIdO1GH2eLNTiWFLx6YW2YkuwiLx3ll+7IjVDDqq9r5349GIJjyrE33ovdfstKGZ2+j3jBGkmduJAvjQjbd+imCSbmompUeag5ZJntDonH+Ek48zsYy9XmonM8ergeIj5darwWrUter81YzPTf769daDzay194dt3Npi3D2AX5sfnXJVQ46pnTuc63uXAkH0gAu880Dnt7B3zMb/La72n3rERX2i7v3jhh9Qx3q11PuWfbOsBlreOy+km8aKeqyZuNiLF/6L23mwRX59bH3aIqP2Pm4m3Be+e6TxeovBcj13ikorCR3ewjhUTNMF2cUtrG83vHebO01P9zbrzMbUbmRuLEx2ebCzsGRkFtZ4URhm7vmAbcs/1EV/829dLcZRlEtOYd+sbBoyTQES7mSALOL8zNwFh5OiWvA3i4veOqAZJ8P/LHgTRT/UeaaNqMETfb6+Wda3VrC+kd8D98/g1H5ZbmuoccayL2RfccTV7Bwo/K6+tNYIj/eTGSBxXmQBljqjSuzetWwkfOZxNvwoDv3MAo70yMly2u0FATqWBSyv4Ew2fHjQhSzsTqA7uMeUVrJjlhurU7tj2TmdgguNweiiWbLwTl4j7U7e8yvAmAi6ew0HEny84lhpDAXU0TEXmK7bLLZw2HwA6y4yNe0LDafdpV3v0irZpuRIXz2RlZUGqIuDDdGELAj6BwmUbCxc1PSOUnI2L6sCJbKH201jv7ZFqTAMoZz6AqZwnTxd9IhiziXe4qzegOqhurSoC5t0uujBBd5hmwQQj9BMDa4xwPVBRcFySnnuRMKHCucQ+eRRiMMeR8fZjt2LUoyxLryanIBMjNHPuUWksbq55aMJcOGeHR2ZCIEPL8oh8cmjEILmk+CuhkVei28McuDT4gRrrwn/7rvqx5I//fvsEqe4wjOqvyE4qFwi+XPMtWxb6dqm0Z25tmnCN08hms/5HhhtgCj2WvpwWqOR6IPFiufaZ4mj0Inz2uk+nexBJ4ZPffu0NwGtgID1GtOsges2Eu0AG0O+HzBjFBryWlfTp9Y9aMkSlsC33WnPJQaD0MAVPdDzivdq2YI8jHd+sbc8La2ES6sDpYkpJY8nDexV9giXvE5jrqUB7/UzEUPAhTkSWXguVSBhDLlRGz6HNxGBUNNVZsKopQ1gfgZUFSDDi1/0lrOQNrrEUYjC8oV3F9sET7LQRq+Q2zIGV5iEIrr3zc9al8cgdjHKgbV/k+V7wRK/A95i+JDo4TGVnoQUj0ds0ZpMfsFowndLMJovI7o+nY1HLvr2otOK8BW93tpI+OKdRDPth/m4l4Nmndjbj/hIQ7+ZdzZf3dW+ffT5qM+UxJEAL7zNyK0IBZSNCWUsbUrnIalRqYSqVb6QtU3+iUFqjs9UtcTnW9rhxYHUDp+paofPt7RDta9SCzRZhU1zLKjwhiyhw2xVK7CEpTWm6ZFaYlmqVliupQV6q5PQabIKmeZY6bc7sykIt8tSUywLEmYlWHDQlEkWZCuJFpSwtNbbKcstsSxVKyzX0kJtKyuDN6kq3CbDRq25inSaVCVt5naCYWJRwmVZKnCWa2mBCn4JnSarkGmOgMqJHB0vp6/+M66kkqMrjAPA6UGQP80HjDXCBdZTChZa3SwuFnBwDgM3PHArxu/6HG6+33PjkfIM0yLLIRpKSy9h/KdDfMhVzIHxrRd0mBc0LDyyaYiyCBTDtocPkb1XKganecwMHBYvS1hUzfChwOc1xXAM72/+HdS8v3C1tMLVf2jq1yDFEB2ejYZ33PA4wNU2nlyC9iBENu5FS/TpoEFMvkdFXbx56NGJe4oh6lTOiqE6aafDBhBLYA2aCEm1qpwDswLWoILlZaXhYKtRuIKxp8rQJQMGrdIeKkZtVTJGXHXx/Aqj60QcMj2VGgaria4BFWJcJ1XH3FGGJoU36PNOF/T83SaQn1IYg9PFh11Wm+rSX5e4TB4YxCnBzPASKIL6Mm+z+7zTRwk96orI7llohSp0XlTJPVpWJHuJyzLJyNXoF5TucO1JfYdXb7P3u2q7q8iQ8eYuBceYWq9lav90IfX59P22/lXGGALpZlJbN7/PXu3Ixuv7/UZh3ayBqBVmrUF8vZZVbRj/8NQjvcszR6B2+no930e82dZfxCrfZ7foCx7St08l/hk/oOXTTRuNQg9iXwg47aeXCXoo0KZsMVh98pPQ8Grz9c//DwKotB2KEwEA</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>