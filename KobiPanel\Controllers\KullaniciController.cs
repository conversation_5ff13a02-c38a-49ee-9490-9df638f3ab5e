using System;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web.Mvc;
using System.Web.Security;
using KobiPanel.Models;
using System.Security.Cryptography;
using System.Text;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Collections.Generic;

namespace KobiPanel.Controllers
{
    public class KullaniciController : Controller
    {
        private KobiPanelDbContext db = new KobiPanelDbContext();

        // GET: Kullanici
        public ActionResult Index()
        {
            try
            {
                ViewBag.PageHeader = "Kullanıcı Yönetimi";
                var kullanicilar = db.Kullanicilar.OrderBy(k => k.AdSoyad).ToList();
                return View(kullanicilar);
            }
            catch (Exception ex)
            {
                TempData["KullaniciHata"] = "Kullanıcılar yüklenirken bir hata oluştu: " + ex.Message;
                // Exception durumunda boş liste gönder ki View'de null reference hatası olmasın
                return View(new List<Kullanicilar>());
            }
        }

        // GET: Kullanici/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }

            try
            {
                Kullanicilar kullanici = db.Kullanicilar.Find(id);
                if (kullanici == null)
                {
                    return HttpNotFound();
                }

                ViewBag.PageHeader = "Kullanıcı Detayları";
                return View(kullanici);
            }
            catch (Exception ex)
            {
                TempData["KullaniciHata"] = "Kullanıcı detayları yüklenirken bir hata oluştu: " + ex.Message;
                return RedirectToAction("Index");
            }
        }

        // GET: Kullanici/Create
        public ActionResult Create()
        {
            ViewBag.PageHeader = "Yeni Kullanıcı Ekle";
            ViewBag.Roller = GetRollerSelectList();
            return View();
        }

        // POST: Kullanici/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(Kullanicilar kullanici)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    // Kullanıcı adı benzersizlik kontrolü
                    if (db.Kullanicilar.Any(k => k.KullaniciAdi == kullanici.KullaniciAdi))
                    {
                        ModelState.AddModelError("KullaniciAdi", "Bu kullanıcı adı zaten kullanılıyor.");
                        ViewBag.Roller = GetRollerSelectList();
                        return View(kullanici);
                    }

                    // E-posta benzersizlik kontrolü
                    if (db.Kullanicilar.Any(k => k.Email == kullanici.Email))
                    {
                        ModelState.AddModelError("Email", "Bu e-posta adresi zaten kullanılıyor.");
                        ViewBag.Roller = GetRollerSelectList();
                        return View(kullanici);
                    }

                    // Şifreyi hashle
                    kullanici.Sifre = HashPassword(kullanici.Sifre);
                    kullanici.KayitTarihi = DateTime.Now;
                    kullanici.Aktif = true;

                    db.Kullanicilar.Add(kullanici);
                    db.SaveChanges();

                    TempData["KullaniciBasarili"] = "Kullanıcı başarıyla eklendi.";
                    return RedirectToAction("Index");
                }

                ViewBag.Roller = GetRollerSelectList();
                return View(kullanici);
            }
            catch (Exception ex)
            {
                TempData["KullaniciHata"] = "Kullanıcı eklenirken bir hata oluştu: " + ex.Message;
                ViewBag.Roller = GetRollerSelectList();
                return View(kullanici);
            }
        }

        // GET: Kullanici/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }

            try
            {
                Kullanicilar kullanici = db.Kullanicilar.Find(id);
                if (kullanici == null)
                {
                    return HttpNotFound();
                }

                ViewBag.PageHeader = "Kullanıcı Düzenle";
                ViewBag.Roller = GetRollerSelectList(kullanici.Rol);

                // Şifreyi temizle (güvenlik için)
                kullanici.Sifre = "";
                kullanici.SifreTekrar = "";

                return View(kullanici);
            }
            catch (Exception ex)
            {
                TempData["KullaniciHata"] = "Kullanıcı düzenleme sayfası yüklenirken bir hata oluştu: " + ex.Message;
                return RedirectToAction("Index");
            }
        }

        // POST: Kullanici/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(Kullanicilar kullanici)
        {
            try
            {
                // Şifre alanlarını model validation'dan çıkar
                ModelState.Remove("Sifre");
                ModelState.Remove("SifreTekrar");

                if (ModelState.IsValid)
                {
                    var mevcutKullanici = db.Kullanicilar.Find(kullanici.Id);
                    if (mevcutKullanici == null)
                    {
                        return HttpNotFound();
                    }

                    // Kullanıcı adı benzersizlik kontrolü (kendisi hariç)
                    if (db.Kullanicilar.Any(k => k.KullaniciAdi == kullanici.KullaniciAdi && k.Id != kullanici.Id))
                    {
                        ModelState.AddModelError("KullaniciAdi", "Bu kullanıcı adı zaten kullanılıyor.");
                        ViewBag.Roller = GetRollerSelectList(kullanici.Rol);
                        return View(kullanici);
                    }

                    // E-posta benzersizlik kontrolü (kendisi hariç)
                    if (db.Kullanicilar.Any(k => k.Email == kullanici.Email && k.Id != kullanici.Id))
                    {
                        ModelState.AddModelError("Email", "Bu e-posta adresi zaten kullanılıyor.");
                        ViewBag.Roller = GetRollerSelectList(kullanici.Rol);
                        return View(kullanici);
                    }

                    // Bilgileri güncelle
                    mevcutKullanici.KullaniciAdi = kullanici.KullaniciAdi;
                    mevcutKullanici.AdSoyad = kullanici.AdSoyad;
                    mevcutKullanici.Email = kullanici.Email;
                    mevcutKullanici.Telefon = kullanici.Telefon;
                    mevcutKullanici.Rol = kullanici.Rol;
                    mevcutKullanici.Aktif = kullanici.Aktif;
                    mevcutKullanici.Aciklama = kullanici.Aciklama;
                    mevcutKullanici.GuncellemeTarihi = DateTime.Now;

                    // Şifre değiştirilmek isteniyorsa
                    if (!string.IsNullOrEmpty(kullanici.Sifre))
                    {
                        if (kullanici.Sifre != kullanici.SifreTekrar)
                        {
                            ModelState.AddModelError("SifreTekrar", "Şifreler eşleşmiyor.");
                            ViewBag.Roller = GetRollerSelectList(kullanici.Rol);
                            return View(kullanici);
                        }
                        mevcutKullanici.Sifre = HashPassword(kullanici.Sifre);
                    }

                    db.Entry(mevcutKullanici).State = EntityState.Modified;
                    db.SaveChanges();

                    TempData["KullaniciBasarili"] = "Kullanıcı başarıyla güncellendi.";
                    return RedirectToAction("Index");
                }

                ViewBag.Roller = GetRollerSelectList(kullanici.Rol);
                return View(kullanici);
            }
            catch (Exception ex)
            {
                TempData["KullaniciHata"] = "Kullanıcı güncellenirken bir hata oluştu: " + ex.Message;
                ViewBag.Roller = GetRollerSelectList(kullanici.Rol);
                return View(kullanici);
            }
        }

        // GET: Kullanici/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }

            try
            {
                Kullanicilar kullanici = db.Kullanicilar.Find(id);
                if (kullanici == null)
                {
                    return HttpNotFound();
                }

                ViewBag.PageHeader = "Kullanıcı Sil";
                return View(kullanici);
            }
            catch (Exception ex)
            {
                TempData["KullaniciHata"] = "Kullanıcı silme sayfası yüklenirken bir hata oluştu: " + ex.Message;
                return RedirectToAction("Index");
            }
        }

        // POST: Kullanici/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            try
            {
                Kullanicilar kullanici = db.Kullanicilar.Find(id);
                if (kullanici == null)
                {
                    TempData["KullaniciHata"] = "Silinmek istenen kullanıcı bulunamadı.";
                    return RedirectToAction("Index");
                }

                string kullaniciAdi = kullanici.AdSoyad ?? "Bilinmeyen Kullanıcı";
                db.Kullanicilar.Remove(kullanici);
                int sonuc = db.SaveChanges();

                if (sonuc > 0)
                {
                    TempData["KullaniciBasarili"] = kullaniciAdi + " kullanıcısı başarıyla silindi.";
                }
                else
                {
                    TempData["KullaniciHata"] = "Kullanıcı silme işlemi başarısız oldu.";
                }

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                TempData["KullaniciHata"] = "Kullanıcı silinirken bir hata oluştu: " + ex.Message;
                return RedirectToAction("Index");
            }
        }

        // Şifre değiştirme
        public ActionResult ChangePassword(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }

            try
            {
                Kullanicilar kullanici = db.Kullanicilar.Find(id);
                if (kullanici == null)
                {
                    return HttpNotFound();
                }

                ViewBag.PageHeader = "Şifre Değiştir";
                ViewBag.KullaniciAdi = kullanici.AdSoyad;
                return View(new ChangePasswordViewModel { KullaniciId = kullanici.Id });
            }
            catch (Exception ex)
            {
                TempData["KullaniciHata"] = "Şifre değiştirme sayfası yüklenirken bir hata oluştu: " + ex.Message;
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult ChangePassword(ChangePasswordViewModel model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var kullanici = db.Kullanicilar.Find(model.KullaniciId);
                    if (kullanici == null)
                    {
                        return HttpNotFound();
                    }

                    // Eski şifre kontrolü
                    if (!VerifyPassword(model.EskiSifre, kullanici.Sifre))
                    {
                        ModelState.AddModelError("EskiSifre", "Eski şifre yanlış.");
                        ViewBag.KullaniciAdi = kullanici.AdSoyad;
                        return View(model);
                    }

                    // Yeni şifre güncelleme
                    kullanici.Sifre = HashPassword(model.YeniSifre);
                    kullanici.GuncellemeTarihi = DateTime.Now;

                    db.Entry(kullanici).State = EntityState.Modified;
                    db.SaveChanges();

                    TempData["KullaniciBasarili"] = "Şifre başarıyla değiştirildi.";
                    return RedirectToAction("Index");
                }

                var user = db.Kullanicilar.Find(model.KullaniciId);
                ViewBag.KullaniciAdi = user?.AdSoyad;
                return View(model);
            }
            catch (Exception ex)
            {
                TempData["KullaniciHata"] = "Şifre değiştirilirken bir hata oluştu: " + ex.Message;
                return RedirectToAction("Index");
            }
        }

        // Helper Methods
        private SelectList GetRollerSelectList(string selectedValue = null)
        {
            var roller = new[]
            {
                new { Value = "Yonetici", Text = "Yönetici" },
                new { Value = "Kullanici", Text = "Kullanıcı" },
                new { Value = "Misafir", Text = "Misafir" }
            };

            return new SelectList(roller, "Value", "Text", selectedValue);
        }

        private string HashPassword(string password)
        {
            using (SHA256 sha256Hash = SHA256.Create())
            {
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(password + "KobiPanelSalt"));
                return Convert.ToBase64String(bytes);
            }
        }

        private bool VerifyPassword(string password, string hashedPassword)
        {
            string hashOfInput = HashPassword(password);
            return hashOfInput == hashedPassword;
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }

    // Şifre değiştirme için ViewModel
    public class ChangePasswordViewModel
    {
        public int KullaniciId { get; set; }

        [Required(ErrorMessage = "Eski şifre zorunludur.")]
        [DisplayName("Eski Şifre")]
        [DataType(DataType.Password)]
        public string EskiSifre { get; set; }

        [Required(ErrorMessage = "Yeni şifre zorunludur.")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Şifre en az 6 karakter olmalıdır.")]
        [DisplayName("Yeni Şifre")]
        [DataType(DataType.Password)]
        public string YeniSifre { get; set; }

        [Required(ErrorMessage = "Şifre tekrarı zorunludur.")]
        [System.ComponentModel.DataAnnotations.Compare("YeniSifre", ErrorMessage = "Şifreler eşleşmiyor.")]
        [DisplayName("Yeni Şifre Tekrarı")]
        [DataType(DataType.Password)]
        public string YeniSifreTekrar { get; set; }
    }
}
