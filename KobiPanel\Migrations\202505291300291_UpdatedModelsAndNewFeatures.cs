namespace KobiPanel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class UpdatedModelsAndNewFeatures : DbMigration
    {
        public override void Up()
        {
            RenameTable(name: "dbo.AlinanYakitlars", newName: "AlinanYakitlar");
            DropForeignKey("dbo.KantarFisi", "TarlaID", "dbo.BicilenTarla");
            DropForeignKey("dbo.BicilenTarla", "MusteriID", "dbo.Musteriler");
            DropIndex("dbo.AlinanYakitlar", new[] { "ID" });
            DropColumn("dbo.AlinanYakitlar", "AracID");
            RenameColumn(table: "dbo.AlinanYakitlar", name: "ID", newName: "AracID");
            CreateTable(
                "dbo.Bildirimler",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Baslik = c.String(nullable: false, maxLength: 200),
                        Mesaj = c.String(nullable: false, maxLength: 1000),
                        Tip = c.String(nullable: false),
                        <PERSON>llaniciId = c.Int(),
                        Okundu = c.<PERSON>(nullable: false),
                        OlusturmaTarihi = c.DateTime(nullable: false),
                        OkunmaTarihi = c.DateTime(),
                        Url = c.String(maxLength: 500),
                        Ikon = c.String(maxLength: 50),
                        Oncelik = c.Int(nullable: false),
                        Aktif = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Kullanicilar", t => t.KullaniciId)
                .Index(t => t.KullaniciId);
            
            AddColumn("dbo.Araclar", "Marka", c => c.String(maxLength: 50));
            AddColumn("dbo.Araclar", "Model", c => c.String(maxLength: 50));
            AddColumn("dbo.Araclar", "ModelYili", c => c.Int());
            AddColumn("dbo.Araclar", "Aciklama", c => c.String(maxLength: 500));
            AddColumn("dbo.Araclar", "KayitTarihi", c => c.DateTime(nullable: false));
            AddColumn("dbo.Araclar", "Aktif", c => c.Boolean(nullable: false));
            AddColumn("dbo.AlinanYakitlar", "LitreBasinaFiyat", c => c.Decimal(precision: 18, scale: 2));
            AddColumn("dbo.AlinanYakitlar", "Aciklama", c => c.String(maxLength: 500));
            AddColumn("dbo.AlinanYakitlar", "KayitTarihi", c => c.DateTime(nullable: false));
            AddColumn("dbo.Kullanicilar", "Email", c => c.String(nullable: false, maxLength: 100));
            AddColumn("dbo.Kullanicilar", "Telefon", c => c.String(maxLength: 15));
            AddColumn("dbo.Kullanicilar", "Rol", c => c.String(nullable: false));
            AddColumn("dbo.Kullanicilar", "Aktif", c => c.Boolean(nullable: false));
            AddColumn("dbo.Kullanicilar", "SonGirisTarihi", c => c.DateTime());
            AddColumn("dbo.Kullanicilar", "KayitTarihi", c => c.DateTime(nullable: false));
            AddColumn("dbo.Kullanicilar", "GuncellemeTarihi", c => c.DateTime());
            AddColumn("dbo.Kullanicilar", "ProfilResmi", c => c.String(maxLength: 255));
            AddColumn("dbo.Kullanicilar", "Aciklama", c => c.String(maxLength: 500));
            AlterColumn("dbo.Araclar", "VizeBitisTarihi", c => c.DateTime());
            AlterColumn("dbo.Araclar", "SigortaBitisTarihi", c => c.DateTime());
            AlterColumn("dbo.Araclar", "EgzozBitisTarihi", c => c.DateTime());
            AlterColumn("dbo.Araclar", "TescilNo", c => c.String(maxLength: 20));
            AlterColumn("dbo.AlinanYakitlar", "AracID", c => c.Int(nullable: false));
            AlterColumn("dbo.AlinanYakitlar", "YakitTutari", c => c.Decimal(nullable: false, precision: 18, scale: 2));
            AlterColumn("dbo.AlinanYakitlar", "YakitinAlindigiYer", c => c.String(nullable: false, maxLength: 100));
            AlterColumn("dbo.AlinanYakitlar", "Litre", c => c.Decimal(nullable: false, precision: 18, scale: 2));
            AlterColumn("dbo.Kullanicilar", "KullaniciAdi", c => c.String(nullable: false, maxLength: 50));
            AlterColumn("dbo.Kullanicilar", "Sifre", c => c.String(nullable: false, maxLength: 255));
            AlterColumn("dbo.Kullanicilar", "AdSoyad", c => c.String(nullable: false, maxLength: 100));
            CreateIndex("dbo.AlinanYakitlar", "AracID");
            AddForeignKey("dbo.KantarFisi", "TarlaID", "dbo.BicilenTarla", "TarlaID");
            AddForeignKey("dbo.BicilenTarla", "MusteriID", "dbo.Musteriler", "ID");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.BicilenTarla", "MusteriID", "dbo.Musteriler");
            DropForeignKey("dbo.KantarFisi", "TarlaID", "dbo.BicilenTarla");
            DropForeignKey("dbo.Bildirimler", "KullaniciId", "dbo.Kullanicilar");
            DropIndex("dbo.Bildirimler", new[] { "KullaniciId" });
            DropIndex("dbo.AlinanYakitlar", new[] { "AracID" });
            AlterColumn("dbo.Kullanicilar", "AdSoyad", c => c.String());
            AlterColumn("dbo.Kullanicilar", "Sifre", c => c.String());
            AlterColumn("dbo.Kullanicilar", "KullaniciAdi", c => c.String());
            AlterColumn("dbo.AlinanYakitlar", "Litre", c => c.Int(nullable: false));
            AlterColumn("dbo.AlinanYakitlar", "YakitinAlindigiYer", c => c.String());
            AlterColumn("dbo.AlinanYakitlar", "YakitTutari", c => c.Int(nullable: false));
            AlterColumn("dbo.AlinanYakitlar", "AracID", c => c.Int(nullable: false, identity: true));
            AlterColumn("dbo.Araclar", "TescilNo", c => c.String());
            AlterColumn("dbo.Araclar", "EgzozBitisTarihi", c => c.DateTime(nullable: false));
            AlterColumn("dbo.Araclar", "SigortaBitisTarihi", c => c.DateTime(nullable: false));
            AlterColumn("dbo.Araclar", "VizeBitisTarihi", c => c.DateTime(nullable: false));
            DropColumn("dbo.Kullanicilar", "Aciklama");
            DropColumn("dbo.Kullanicilar", "ProfilResmi");
            DropColumn("dbo.Kullanicilar", "GuncellemeTarihi");
            DropColumn("dbo.Kullanicilar", "KayitTarihi");
            DropColumn("dbo.Kullanicilar", "SonGirisTarihi");
            DropColumn("dbo.Kullanicilar", "Aktif");
            DropColumn("dbo.Kullanicilar", "Rol");
            DropColumn("dbo.Kullanicilar", "Telefon");
            DropColumn("dbo.Kullanicilar", "Email");
            DropColumn("dbo.AlinanYakitlar", "KayitTarihi");
            DropColumn("dbo.AlinanYakitlar", "Aciklama");
            DropColumn("dbo.AlinanYakitlar", "LitreBasinaFiyat");
            DropColumn("dbo.Araclar", "Aktif");
            DropColumn("dbo.Araclar", "KayitTarihi");
            DropColumn("dbo.Araclar", "Aciklama");
            DropColumn("dbo.Araclar", "ModelYili");
            DropColumn("dbo.Araclar", "Model");
            DropColumn("dbo.Araclar", "Marka");
            DropTable("dbo.Bildirimler");
            RenameColumn(table: "dbo.AlinanYakitlar", name: "AracID", newName: "ID");
            AddColumn("dbo.AlinanYakitlar", "AracID", c => c.Int(nullable: false));
            CreateIndex("dbo.AlinanYakitlar", "ID");
            AddForeignKey("dbo.BicilenTarla", "MusteriID", "dbo.Musteriler", "ID", cascadeDelete: true);
            AddForeignKey("dbo.KantarFisi", "TarlaID", "dbo.BicilenTarla", "TarlaID", cascadeDelete: true);
            RenameTable(name: "dbo.AlinanYakitlar", newName: "AlinanYakitlars");
        }
    }
}
