using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace KobiPanel.Models
{
    [Table("Bildirimler")]
    public class Bildirim
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required(ErrorMessage = "Başlık zorunludur.")]
        [StringLength(200, ErrorMessage = "Başlık en fazla 200 karakter olabilir.")]
        [DisplayName("Başlık")]
        public string Baslik { get; set; }

        [Required(ErrorMessage = "Mesaj zorunludur.")]
        [StringLength(1000, ErrorMessage = "Mesaj en fazla 1000 karakter olabilir.")]
        [DisplayName("Mesaj")]
        public string Mesaj { get; set; }

        [Required(ErrorMessage = "Tip zorunludur.")]
        [DisplayName("Bildirim Tipi")]
        public string Tip { get; set; }

        [DisplayName("Kullanıcı ID")]
        public int? KullaniciId { get; set; }

        [DisplayName("Okundu")]
        public bool Okundu { get; set; }

        [DisplayName("Oluşturma Tarihi")]
        [DataType(DataType.DateTime)]
        public DateTime OlusturmaTarihi { get; set; }

        [DisplayName("Okunma Tarihi")]
        [DataType(DataType.DateTime)]
        public DateTime? OkunmaTarihi { get; set; }

        [DisplayName("URL")]
        [StringLength(500)]
        public string Url { get; set; }

        [DisplayName("İkon")]
        [StringLength(50)]
        public string Ikon { get; set; }

        [DisplayName("Öncelik")]
        public int Oncelik { get; set; }

        [DisplayName("Aktif")]
        public bool Aktif { get; set; }

        // Navigation Properties
        [ForeignKey("KullaniciId")]
        public virtual Kullanicilar Kullanici { get; set; }
    }

    // Bildirim tipleri için enum
    public enum BildirimTipleri
    {
        [Display(Name = "Bilgi")]
        Bilgi,
        [Display(Name = "Uyarı")]
        Uyari,
        [Display(Name = "Hata")]
        Hata,
        [Display(Name = "Başarı")]
        Basari,
        [Display(Name = "Sistem")]
        Sistem
    }

    // Bildirim öncelikleri
    public enum BildirimOncelikleri
    {
        [Display(Name = "Düşük")]
        Dusuk = 1,
        [Display(Name = "Normal")]
        Normal = 2,
        [Display(Name = "Yüksek")]
        Yuksek = 3,
        [Display(Name = "Kritik")]
        Kritik = 4
    }
}
