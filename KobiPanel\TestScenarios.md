# KobiPanel Test Senaryoları

## 🧪 Test Planı ve Senaryoları

### 1. **Kullanıcı Yönetimi Testleri**

#### ✅ **Kullanıcı Ekleme Testi**
1. `/Kullan<PERSON>/Create` sayfasına git
2. Tü<PERSON> zor<PERSON>lu alanları doldur:
   - Ad Soyad: "Test Kullanıcı"
   - Kullanı<PERSON><PERSON> Adı: "testuser"
   - E-posta: "<EMAIL>"
   - Rol: "Kullanici"
   - <PERSON><PERSON><PERSON>: "123456"
   - <PERSON><PERSON><PERSON>rı: "123456"
3. "Kullanıcı Ekle" butonuna tıkla
4. **Beklenen Sonuç:** Başarı mesajı ve kullanıcı listesine yönlendirme

#### ✅ **Kullanıcı Düzenleme Testi**
1. `/Kullanici` sayfasında bir kullanıcının "Düzenle" butonuna tıkla
2. E-posta adresini değiştir
3. "Güncelle" butonuna tıkla
4. **Beklenen Sonuç:** Başarı mesajı ve güncellenmiş bilgiler

#### ✅ **Şifre Değiştirme Testi**
1. <PERSON><PERSON> kullanıcının "Şifre Değiştir" butonuna tıkla
2. Eski şifre, yeni şifre ve tekrarını gir
3. "Şifre Değiştir" butonuna tıkla
4. **Beklenen Sonuç:** Başarı mesajı

### 2. **Araç Yönetimi Testleri**

#### ✅ **Yakıt Ekleme Testi**
1. `/Arac/YakitEkle` sayfasına git
2. Formu doldur:
   - Araç: Dropdown'dan seç
   - Yakıt Tutarı: "500"
   - Litre: "50"
   - Yakıtın Alındığı Yer: "Shell"
   - Alış Tarihi: Bugünün tarihi
3. "Kaydet" butonuna tıkla
4. **Beklenen Sonuç:** Başarı mesajı ve litre başına fiyat otomatik hesaplanması

#### ✅ **Araç Ekleme Testi**
1. `/Arac/AracEkle` sayfasına git
2. Araç bilgilerini gir
3. Tarih alanlarını doldur
4. "Kaydet" butonuna tıkla
5. **Beklenen Sonuç:** Başarı mesajı ve araç listesine ekleme

### 3. **Biçme İşleri Testleri**

#### ✅ **Kantar Fişi Ekleme Testi**
1. `/Bicme/KantarFisiEkle` sayfasına git
2. Formu doldur:
   - Tarla: Dropdown'dan seç
   - Birinci Tartım: "1000"
   - İkinci Tartım: "500"
   - Tartım Tarihi: Bugün
3. "Kaydet" butonuna tıkla
4. **Beklenen Sonuç:** Net KG otomatik hesaplanması ve başarı mesajı

#### ✅ **Kantar Fişi Listesi Testi**
1. `/Bicme/KantarFisiAnasayfa` sayfasına git
2. **Beklenen Sonuç:** Kantar fişlerinin listelenmesi, müşteri bilgileriyle birlikte

### 4. **Raporlama Testleri**

#### ✅ **Satış Raporları Testi**
1. `/Rapor/SatisRaporlari` sayfasına git
2. Tarih aralığı seç
3. Müşteri filtresi uygula
4. "Rapor Oluştur" butonuna tıkla
5. **Beklenen Sonuç:** Filtrelenmiş satış listesi ve istatistikler

#### ✅ **Aylık Özet Raporu Testi**
1. `/Rapor/AylikOzet` sayfasına git
2. Yıl ve ay seç
3. **Beklenen Sonuç:** Seçilen aya ait gelir-gider analizi

### 5. **Dashboard Testleri**

#### ✅ **Ana Sayfa İstatistikleri Testi**
1. Ana sayfaya git (`/`)
2. **Kontrol Edilecekler:**
   - Tüm istatistik kartları görüntüleniyor
   - Sayılar doğru hesaplanıyor
   - Kar/Zarar durumu doğru gösteriliyor
   - Hızlı erişim butonları çalışıyor

### 6. **Dark/Light Theme Testleri**

#### ✅ **Tema Değiştirme Testi**
1. Navbar'daki tema toggle butonuna tıkla
2. **Beklenen Sonuç:** Tema değişmesi ve kullanıcı tercihinin kaydedilmesi
3. Sayfayı yenile
4. **Beklenen Sonuç:** Seçilen temanın korunması

### 7. **Güvenlik Testleri**

#### ✅ **Null Reference Testleri**
1. Boş veritabanı ile tüm sayfaları ziyaret et
2. **Beklenen Sonuç:** Hiçbir sayfada null reference hatası alınmaması

#### ✅ **Validation Testleri**
1. Tüm formlarda zorunlu alanları boş bırak
2. **Beklenen Sonuç:** Uygun hata mesajları gösterilmesi
3. Geçersiz e-posta formatı gir
4. **Beklenen Sonuç:** E-posta validation hatası

### 8. **Performans Testleri**

#### ✅ **Sayfa Yükleme Hızı Testi**
1. Tüm ana sayfaları ziyaret et
2. **Beklenen Sonuç:** Sayfaların 3 saniyeden kısa sürede yüklenmesi

#### ✅ **Büyük Veri Testi**
1. 100+ kayıt içeren listeleri görüntüle
2. **Beklenen Sonuç:** Sayfaların hızlı yüklenmesi ve pagination çalışması

---

## 🔧 **Test Ortamı Hazırlığı**

### **Gerekli Test Verileri:**

1. **Kullanıcılar:** En az 3 farklı rol ile kullanıcı
2. **Müşteriler:** En az 10 müşteri
3. **Ürünler:** En az 5 ürün
4. **Araçlar:** En az 2 araç
5. **Satışlar:** En az 20 satış kaydı
6. **Giderler:** En az 15 gider kaydı
7. **Tarlalar:** En az 5 tarla kaydı

### **Test Adımları:**

1. **Veritabanını temizle** (opsiyonel)
2. **Migration'ları çalıştır**
3. **Test verilerini ekle**
4. **Tüm test senaryolarını çalıştır**
5. **Sonuçları kaydet**

---

## 📊 **Test Sonuçları Şablonu**

| Test Senaryosu | Durum | Notlar |
|----------------|-------|--------|
| Kullanıcı Ekleme | ✅/❌ | |
| Yakıt Ekleme | ✅/❌ | |
| Kantar Fişi | ✅/❌ | |
| Satış Raporları | ✅/❌ | |
| Dashboard | ✅/❌ | |
| Tema Değiştirme | ✅/❌ | |

---

## 🚨 **Kritik Test Noktaları**

1. **Null Reference Hatalar:** Hiçbir sayfada null reference hatası olmamalı
2. **Navigation Properties:** Tüm ilişkili veriler doğru yüklenmeli
3. **Validation:** Form validationları çalışmalı
4. **Theme System:** Dark/Light tema geçişi sorunsuz olmalı
5. **Responsive Design:** Mobil cihazlarda düzgün görünmeli

---

## 📝 **Test Tamamlandıktan Sonra:**

1. **Hata raporları oluştur**
2. **Performans iyileştirmeleri belirle**
3. **Kullanıcı deneyimi geri bildirimleri topla**
4. **Production deployment planı hazırla**
