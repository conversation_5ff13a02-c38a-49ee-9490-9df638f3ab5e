﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using KobiPanel.Models;
using System.Web.Configuration;

namespace KobiPanel.Filters
{
    public class ExcFilter : FilterAttribute, IExceptionFilter
    {
        public void OnException(ExceptionContext filterContext)
        {
            try
            {
                if (filterContext?.Exception != null)
                {
                    // Exception'ı database'e kaydet
                    LogErrorToDatabase(filterContext);

                    filterContext.ExceptionHandled = true;

                    // Exception türüne göre kullanıcı dostu mesajlar
                    string userMessage = GetUserFriendlyMessage(filterContext.Exception);
                    string severity = GetErrorSeverity(filterContext.Exception);

                    // Development mode'da detaylı hata göster
                    bool isDevelopment = IsDebugMode();

                    if (filterContext.Controller?.TempData != null)
                    {
                        filterContext.Controller.TempData["ErrorMessage"] = userMessage;
                        filterContext.Controller.TempData["ErrorSeverity"] = severity;

                        if (isDevelopment)
                        {
                            filterContext.Controller.TempData["ErrorDetails"] = filterContext.Exception.Message;
                            filterContext.Controller.TempData["ErrorStackTrace"] = filterContext.Exception.StackTrace;
                        }
                    }

                    // Ajax request ise JSON döndür
                    if (filterContext.HttpContext.Request.IsAjaxRequest())
                    {
                        object responseData;

                        if (isDevelopment)
                        {
                            responseData = new {
                                success = false,
                                message = userMessage,
                                severity = severity,
                                details = filterContext.Exception.Message,
                                stackTrace = filterContext.Exception.StackTrace
                            };
                        }
                        else
                        {
                            responseData = new {
                                success = false,
                                message = userMessage,
                                severity = severity
                            };
                        }

                        filterContext.Result = new JsonResult
                        {
                            Data = responseData,
                            JsonRequestBehavior = JsonRequestBehavior.AllowGet
                        };
                    }
                    else
                    {
                        // Normal request - Error sayfasına yönlendir (Login'e değil!)
                        filterContext.Result = new ViewResult
                        {
                            ViewName = "Error",
                            ViewData = new ViewDataDictionary
                            {
                                ["ErrorMessage"] = userMessage,
                                ["ErrorSeverity"] = severity,
                                ["ShowDetails"] = isDevelopment,
                                ["ErrorDetails"] = isDevelopment ? filterContext.Exception.Message : null,
                                ["ErrorStackTrace"] = isDevelopment ? filterContext.Exception.StackTrace : null
                            }
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                // Exception handler'da bile hata olursa - son çare
                System.Diagnostics.Debug.WriteLine($"ExcFilter kritik hatası: {ex.Message}");

                filterContext.Result = new ViewResult
                {
                    ViewName = "Error",
                    ViewData = new ViewDataDictionary
                    {
                        ["ErrorMessage"] = "Beklenmedik bir sistem hatası oluştu. Lütfen sistem yöneticisi ile iletişime geçin.",
                        ["ErrorSeverity"] = "Critical"
                    }
                };
            }
        }

        private void LogErrorToDatabase(ExceptionContext filterContext)
        {
            try
            {
                using (var db = new KobiPanelDbContext())
                {
                    var errorLog = new ErrorLog
                    {
                        ErrorMessage = filterContext.Exception.Message,
                        StackTrace = filterContext.Exception.StackTrace,
                        ControllerName = filterContext.RouteData?.Values["controller"]?.ToString(),
                        ActionName = filterContext.RouteData?.Values["action"]?.ToString(),
                        HttpMethod = filterContext.HttpContext.Request.HttpMethod,
                        RequestUrl = filterContext.HttpContext.Request.Url?.ToString(),
                        UserIP = GetClientIP(filterContext.HttpContext),
                        UserAgent = filterContext.HttpContext.Request.UserAgent,
                        KullaniciAdi = filterContext.HttpContext.User?.Identity?.Name ?? "Anonim",
                        ErrorDate = DateTime.Now,
                        ExceptionType = filterContext.Exception.GetType().Name,
                        InnerException = filterContext.Exception.InnerException?.Message,
                        Severity = GetErrorSeverity(filterContext.Exception),
                        IsResolved = false
                    };

                    db.ErrorLogs.Add(errorLog);
                    db.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                // Database'e kaydedemezse en azından debug'a yaz
                System.Diagnostics.Debug.WriteLine($"Error logging failed: {ex.Message}");
            }
        }

        private string GetUserFriendlyMessage(Exception exception)
        {
            switch (exception)
            {
                case NullReferenceException _:
                    return "Veri erişim hatası oluştu. Sayfa yeniden yüklenecek.";

                case ArgumentNullException _:
                    return "Gerekli bilgiler eksik. Lütfen tüm alanları doldurun.";

                case UnauthorizedAccessException _:
                    return "Bu işlem için yetkiniz bulunmuyor.";

                case TimeoutException _:
                    return "İşlem zaman aşımına uğradı. Lütfen tekrar deneyin.";

                case System.Data.SqlClient.SqlException sqlEx:
                    return "Veritabanı bağlantı sorunu. Lütfen daha sonra tekrar deneyin.";

                case HttpException httpEx when httpEx.GetHttpCode() == 404:
                    return "Aradığınız sayfa bulunamadı.";

                case HttpException httpEx when httpEx.GetHttpCode() == 500:
                    return "Sunucu hatası oluştu. Teknik ekip bilgilendirildi.";

                default:
                    return "Beklenmedik bir hata oluştu. Lütfen tekrar deneyin.";
            }
        }

        private string GetErrorSeverity(Exception exception)
        {
            switch (exception)
            {
                case NullReferenceException _:
                case ArgumentNullException _:
                    return "Medium";

                case UnauthorizedAccessException _:
                    return "High";

                case System.Data.SqlClient.SqlException _:
                case TimeoutException _:
                    return "High";

                case OutOfMemoryException _:
                case StackOverflowException _:
                    return "Critical";

                default:
                    return "Low";
            }
        }

        private bool IsDebugMode()
        {
            try
            {
                return HttpContext.Current.IsDebuggingEnabled;
            }
            catch
            {
                return false;
            }
        }

        private string GetClientIP(HttpContextBase context)
        {
            try
            {
                string ip = context.Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
                if (string.IsNullOrEmpty(ip))
                {
                    ip = context.Request.ServerVariables["REMOTE_ADDR"];
                }
                if (string.IsNullOrEmpty(ip))
                {
                    ip = context.Request.UserHostAddress;
                }
                return ip ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }
    }
}