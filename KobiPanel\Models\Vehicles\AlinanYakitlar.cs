﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace KobiPanel.Models.Vehicles
{
    [Table("AlinanYakitlar")]
    public class AlinanYakitlar
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        [Required(ErrorMessage = "Araç seçimi zorunludur.")]
        [DisplayName("Araç")]
        public int AracID { get; set; }

        [Required(ErrorMessage = "Yakıt tutarı zorunludur.")]
        [DisplayName("Yakıt Tutarı (TL)")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Yakıt tutarı 0'dan büyük olmalıdır.")]
        public decimal YakitTutari { get; set; }

        [Required(ErrorMessage = "Yakıtın alındığı yer zorunludur.")]
        [DisplayName("Yakıtın Alındığı Yer")]
        [StringLength(100, ErrorMessage = "Yakıtın alındığı yer en fazla 100 karakter olabilir.")]
        public string YakitinAlindigiYer { get; set; }

        [DisplayName("Yakıtın Alındığı Anki KM")]
        [Range(0, int.MaxValue, ErrorMessage = "KM değeri 0'dan büyük olmalıdır.")]
        public int? YakitinAlindigiKM { get; set; }

        [Required(ErrorMessage = "Alış tarihi zorunludur.")]
        [DisplayName("Alış Tarihi")]
        [DataType(DataType.DateTime)]
        public DateTime AlisTarihi { get; set; }

        [Required(ErrorMessage = "Litre miktarı zorunludur.")]
        [DisplayName("Litre")]
        [Range(0.1, double.MaxValue, ErrorMessage = "Litre miktarı 0'dan büyük olmalıdır.")]
        public decimal Litre { get; set; }

        [DisplayName("Litre Başına Fiyat")]
        public decimal? LitreBasinaFiyat { get; set; }

        [DisplayName("Açıklama")]
        [StringLength(500)]
        public string Aciklama { get; set; }

        [DisplayName("Kayıt Tarihi")]
        [DataType(DataType.DateTime)]
        public DateTime KayitTarihi { get; set; }

        // Navigation Properties
        [ForeignKey("AracID")]
        public virtual Araclar Arac { get; set; }
    }
}